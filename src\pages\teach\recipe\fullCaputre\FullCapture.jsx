import React, { Fragment, useEffect, useState } from 'react';
import Display from './Display';
import { useTranslation } from 'react-i18next';
import { GreenDefaultButtonConfigProvider, GreenPrimaryButtonConfigProvider } from '../../../../common/styledComponent';
import { Button, ConfigProvider } from 'antd';
import TwoDCapture from './TwoDCapture';
import ThreeDCapture from './ThreeDCapture';
import { isAOI2DSMT } from '../../../../common/const';
import _ from 'lodash';
import { useProductInspectablesRegisterMutation, useUpdateProductMutation } from '../../../../services/product';
import { ALERT_TYPES, aoiAlert } from '../../../../common/alert';
import { useLazyGetCameraCaptureFrameUriQuery } from '../../../../services/camera';
import { useDispatch, useSelector } from 'react-redux';
import { setContainerLvlLoadingMsg, setIsContainerLvlLoadingEnabled } from '../../../../reducer/setting';


const FullCapture = (props) => {
  const {
    curProduct,
    refetchCurProduct,
    setTeachTab,
  } = props;

  const dispatch = useDispatch();

  const { t } = useTranslation();

  const [selectedDimensionConfig, setSelectedDimensionConfig] = useState('2d'); // 2d, 3d
  const [d2CaptureSettings, setD2CaptureSettings] = useState({});
  const [frame, setFrame] = useState({});
  const [algorithmParams, setAlgorithmParams] = useState({});
  const [curImageUri, setCurImageUri] = useState('');
  const [curDepthImgUri, setCurDepthImgUri] = useState('');
  const [manuallUpdateState, setManualUpdateState] = useState(0);
  const [selectedTool, setSelectedTool] = useState('transform');

  const [updateProduct] = useUpdateProductMutation();
  const [getCameraCaptureFrameUri] = useLazyGetCameraCaptureFrameUriQuery();
  const [productInspectablesRegister] = useProductInspectablesRegisterMutation();

  const conveyorAccessToken = useSelector((state) => state.setting.conveyorAccessToken);

  const handleFullCapture = async ({
    d2CaptureSettings,
    frame,
    algorithmParams,
    curProduct,
    conveyorAccessToken,
    manuallUpdateState,
  }) => {
    if (!_.isString(conveyorAccessToken) || _.isEmpty(conveyorAccessToken)) return;

    dispatch(setIsContainerLvlLoadingEnabled(true));
    dispatch(setContainerLvlLoadingMsg(t('loader.productRegister')));

    // save capture settings
    let updatedProduct = _.cloneDeep(curProduct);
    updatedProduct = _.set(updatedProduct, 'product_specific_sensor_config.sensors[0].camera_configs[0].config_2d', d2CaptureSettings);
    updatedProduct = _.set(updatedProduct, 'product_specific_sensor_config.sensors[0].camera_configs[0].config_3d.frame_settings', [frame]);
    updatedProduct = _.set(updatedProduct, 'product_specific_sensor_config.sensors[0].camera_configs[0].config_3d.algorithm_params', algorithmParams);

    const res = await updateProduct({
      product_id: Number(_.get(curProduct, 'product_id', 0)),
      product_specific_sensor_config: updatedProduct.product_specific_sensor_config,
    });

    if (res.error) {
      console.error('updateProduct error', res.error.message);
      aoiAlert(t('notification.error.updateProduct'), ALERT_TYPES.COMMON_ERROR);
      dispatch(setIsContainerLvlLoadingEnabled(false));
      dispatch(setContainerLvlLoadingMsg(''));
      return;
    }

    // start capture
    // const captureRes = await getCameraCaptureFrameUri({
    //   camera_id: 0,
    // });

    // if (captureRes.error) {
    //   aoiAlert(t('notification.error.getCameraCaptureFrameUri'), ALERT_TYPES.COMMON_ERROR);
    //   console.error('getCameraCaptureFrameUri error:', captureRes.error.message);
      // dispatch(setIsContainerLvlLoadingEnabled(false));
      // dispatch(setContainerLvlLoadingMsg(''));
    //   return;
    // }

    // setCurImageUri(_.get(captureRes, 'data.image.data_uri', ''));
    // setCurDepthImgUri(_.get(captureRes, 'data.depth_image.data_uri', ''));

    // instead of calling capture we do inspectables register
    const registerRes = await productInspectablesRegister({
      product_id: Number(_.get(curProduct, 'product_id', 0)),
      conveyor_access_token: conveyorAccessToken,
    });

    if (registerRes.error) {
      aoiAlert(t('notification.error.productInspectablesRegister'), ALERT_TYPES.COMMON_ERROR);
      console.error('productInspectablesRegister error:', registerRes.error.message);
      dispatch(setIsContainerLvlLoadingEnabled(false));
      dispatch(setContainerLvlLoadingMsg(''));
      return;
    }

    dispatch(setIsContainerLvlLoadingEnabled(false));
    dispatch(setContainerLvlLoadingMsg(''));

    const newProdRes = await refetchCurProduct();

    // console.log('newProdRes', newProdRes);
    // console.log('compare color map', _.get(newProdRes, 'data.inspectables[0].color_map_uri') === _.get(curProduct, 'inspectables[0].color_map_uri'));

    // NOTE: if registere product the color/depth map uri will NOT be different
    setCurImageUri(_.get(newProdRes, 'data.inspectables[0].color_map_uri'));
    setCurDepthImgUri(_.get(newProdRes, 'data.inspectables[0].depth_map_uri'));
    setManualUpdateState(manuallUpdateState + 1);
  };

  useEffect(() => {
    // init capture related settings
    // we only have one camera and one step for now
    setFrame(_.get(curProduct, 'product_specific_sensor_config.sensors[0].camera_configs[0].config_3d.frame_settings[0]', {}));
    setD2CaptureSettings(_.get(curProduct, 'product_specific_sensor_config.sensors[0].camera_configs[0].config_2d', {}));
    setAlgorithmParams(_.get(curProduct, 'product_specific_sensor_config.sensors[0].camera_configs[0].config_3d.algorithm_params', {}));
  }, [curProduct]);

  return (
    <div className='flex px-0.5 gap-0.5 flex-1 self-stretch rounded-[6px]'>
      <div className='flex w-[320px] flex-col self-stretch bg-[#ffffff0d]'>
        <div className='flex p-4 gap-4 flex-col justify-center self-stretch'>
          <span className='font-source text-[14px] font-normal leading-[150%] tracking-[0.42px]'>
            {t('common.capture')}
          </span>
          <div className='flex gap-2 items-center'>
            {_.isEmpty(_.get(curProduct, 'inspectables', [])) ?
              <GreenPrimaryButtonConfigProvider>
                <Button
                  type='primary'
                  onClick={() => {
                    handleFullCapture({
                      d2CaptureSettings,
                      frame,
                      algorithmParams,
                      curProduct,
                      conveyorAccessToken,
                      manuallUpdateState,
                    });
                  }}
                >
                  <div className='flex gap-2 items-center'>
                    <img src='/icn/play_black.svg' alt='play' className='w-[8.5px] h-[10px]' />
                    <span className='font-source text-[12px] font-semibold leading-[150%] pt-0.5'>
                      {t(isAOI2DSMT ? 'productDefine.capture2D' : 'productDefine.capture2dAnd3d')}
                    </span>
                  </div>
                </Button>
              </GreenPrimaryButtonConfigProvider>
            :
              <Fragment>
                <GreenDefaultButtonConfigProvider>
                  <Button
                    type='default'
                    onClick={() => {
                      handleFullCapture({
                        d2CaptureSettings,
                        frame,
                        algorithmParams,
                        curProduct,
                        conveyorAccessToken,
                        manuallUpdateState,
                      });
                    }}
                  >
                    <div className='flex gap-2 items-center'>
                      <img src='/icn/play_white.svg' alt='play' className='w-[8.5px] h-[10px]' />
                      <span className='font-source text-[12px] font-semibold leading-[150%] pt-0.5'>
                        {t(isAOI2DSMT ? 'productDefine.capture2D' : 'productDefine.capture2dAnd3d')}
                      </span>
                    </div>
                  </Button>
                </GreenDefaultButtonConfigProvider>
                <GreenPrimaryButtonConfigProvider>
                  <Button
                    type='primary'
                    onClick={() => {
                      setTeachTab('components');
                    }}
                  >
                    <div className='flex gap-2 items-center'>
                      <span className='font-source text-[12px] font-semibold leading-[150%] pt-0.5'>
                        {t('productDefine.editComponents')}
                      </span>
                    </div>
                  </Button>
                </GreenPrimaryButtonConfigProvider>
              </Fragment>
            }
          </div>
          {/* <span className='font-source text-[14px] font-normal leading-[150%]'>
            {t('productDefine.performAFullCapture')}
          </span> */}
        </div>
        <div className='flex flex-col gap-0.5 self-stretch flex-1'>
          <div className='flex py-0.5 px-1 items-center self-stretch gap-1'>
            <ConfigProvider
              theme={{
                components: {
                  Button: {
                    primaryColor: '#fff',
                    colorPrimary: '#ffffff0d',
                    colorPrimaryHover: '#ffffff0d',
                    colorPrimaryActive: '#ffffff0d',
                  }
                }
              }}
            >
              <Button
                style={{ width: '50%' }}
                type={selectedDimensionConfig === '2d' ? 'primary' : 'text'}
                onClick={() => setSelectedDimensionConfig('2d')}
              >
                <span className={`font-source text-[12px] ${selectedDimensionConfig === '2d' ? 'font-semibold' : 'font-normal'} leading-[150%]`}>
                  {t('productDefine.twoDCapture')}
                </span>
              </Button>
              {!isAOI2DSMT && (
                <Button
                  style={{ width: '50%' }}
                  type={selectedDimensionConfig === '3d' ? 'primary' : 'text'}
                  onClick={() => setSelectedDimensionConfig('3d')}
                >
                  <span className={`font-source text-[12px] ${selectedDimensionConfig === '3d' ? 'font-semibold' : 'font-normal'} leading-[150%]`}>
                    {t('productDefine.threeDCapture')}
                  </span>
                </Button>
              )}
            </ConfigProvider>
          </div>
          {selectedDimensionConfig === '2d' && (
            <TwoDCapture
              d2CaptureSettings={d2CaptureSettings}
              setD2CaptureSettings={setD2CaptureSettings}
            />
          )}
          {!isAOI2DSMT && selectedDimensionConfig === '3d' && (
            <ThreeDCapture
              frame={frame}
              setFrame={setFrame}
              algorithmParams={algorithmParams}
              setAlgorithmParams={setAlgorithmParams}
            />
          )}
        </div>
      </div>
      <Display
        curImageUri={curImageUri}
        curDepthImgUri={curDepthImgUri}
        manuallUpdateState={manuallUpdateState}
        fullCaptureDisplayed={true}
        isInAutoGenSetting={false}
        selectedTool={selectedTool}
        setSelectedTool={setSelectedTool}
      />
    </div>
  );
};

export default FullCapture;