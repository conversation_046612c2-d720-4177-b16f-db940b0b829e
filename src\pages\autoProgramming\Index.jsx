import { <PERSON><PERSON>, Collapse, ConfigProvider, Steps } from 'antd';
import React, { Fragment, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import BasicInfo from './BasicInfo';
import ConveyorSetup from './ConveyorSetup';
import { useAddProductMutation, useGetAutoProgramConfigQuery, useGetProductByIdQuery, useRunFullAutoProgramMutation, useUpdateProductMutation } from '../../services/product';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { store } from '../../store';
import { useReleaseConveyorControlMutation } from '../../services/conveyor';
import { useReleaseCameraControlMutation } from '../../services/camera';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import { setCameraAccessToken, setContainerLvlLoadingMsg, setConveyorAccessToken, setCurrentControlledConveyorSlotId, setIsAgentParamRegenRunning, setIsContainerLvlLoadingEnabled, setIsProgrammingUsingConveyor, setShouldRunReevaluateAfterRetrain } from '../../reducer/setting';
import { COMMON_HTTP_CODE } from '../../common/const';
import _ from 'lodash';
import PCBDimension from './PCBDimension';
import FullCapture from './FullCapture';
import UploadCAD from './uploadCAD/Index';
import { useModelUpdateTriggerMutation } from '../../services/inference';
import { modelTypes } from '../../common/const';
import { setIsTrainingRunning, setCurTrainingTaskStartTime } from '../../reducer/setting';
import { sleep } from '../../common/util';


const AutoProgramming = () => {
  const [searchParams] = useSearchParams();
  const productId = searchParams.get('product-id');

  const { t } = useTranslation();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const childrenContainerRef = useRef();

  const [currentStep, setCurrentStep] = useState(0);
  const [currentAddCADStep, setCurrentAddCADStep] = useState(-1); // -1: upload  0: parse, user does not need to align and config in auto programming's case

  const [productName, setProductName] = useState('');
  const [panelWidth, setPanelWidth] = useState(1);
  const [panelHeight, setPanelHeight] = useState(1);
  const [arrayRow, setArrayRow] = useState(1);
  const [arrayColumn, setArrayColumn] = useState(1);
  const [conveyorWidth, setConveyorWidth] = useState(50);
  const [selectedInspectionTypes, setSelectedInspectionTypes] = useState({}); // a map { "mount": [...], "polarity": [...] }
  const [childrenContainerDimension, setChildrenContainerDimension] = useState({
    width: 0,
    height: 0,
  });

  const [autoProgramInspectionRegion, setAutoProgramInspectionRegion] = useState(null); // { pmin, pmax }

  const { data: curProduct, refetch: refetchCurProduct } = useGetProductByIdQuery(productId);
  const [releaseConveyorControl] = useReleaseConveyorControlMutation();
  const [releaseCameraControl] = useReleaseCameraControlMutation();
  const [addProduct] = useAddProductMutation();
  const [updateProduct] = useUpdateProductMutation();
  const [runFullAutoProgram] = useRunFullAutoProgramMutation();
  const [retrainTrigger] = useModelUpdateTriggerMutation();
  const { data: autoProgrammingConfigTemplate, refetch: refetchAutoProgrammingConfigTemplate } = useGetAutoProgramConfigQuery();

  const handleAutoGenerateAgentParams = async (productId) => {
    dispatch(setIsContainerLvlLoadingEnabled(true));
    dispatch(setContainerLvlLoadingMsg(t('loader.autoGeneratingAgentParams')));

    const res = await retrainTrigger({
      model_types: [
        modelTypes.mounting3DModel,
        modelTypes.lead3DModel,
        modelTypes.solder3DModel,
        modelTypes.lead2DV2Model,
        modelTypes.solder2DModel,
        modelTypes.leadModel,
        modelTypes.mountingModel,
        // modelTypes.textDirectionModel,
      ],
      golden_product_id: Number(productId),
      update_parameters: true,
    });

    await sleep(3 * 1000); // ensure task schuedule is written in db

    if (res.error) {
      aoiAlert(t('notification.error.autoGenerateAgentParams'), ALERT_TYPES.COMMON_ERROR);
      console.error('retrainTrigger error:', _.get(res, 'error.message', ''));
      dispatch(setIsContainerLvlLoadingEnabled(false));
      dispatch(setContainerLvlLoadingMsg(''));
      return false;
    }

    // dispatch(setIsContainerLvlLoadingEnabled(true));
    // dispatch(setContainerLvlLoadingMsg(t('loader.refetchingFeatures')));
    dispatch(setIsAgentParamRegenRunning(true));
    dispatch(setIsTrainingRunning(true));
    dispatch(setCurTrainingTaskStartTime(new Date().getTime()));
    return true;
  };

  const conveyorAccessToken = useSelector((state) => state.setting.conveyorAccessToken);

  const releaseConveyorAndCamera = async (conveyorAccessToken, cameraAccessToken) => {
    if (!_.isEmpty(conveyorAccessToken)) {
      // auto release conveyor
      const res = await releaseConveyorControl(conveyorAccessToken);

      if (res.error) {
        aoiAlert(t('notification.error.releaseConveyorControl'), ALERT_TYPES.COMMON_ERROR);
        console.error('releaseConveyorControl error:', _.get(res, 'error.message', ''));
        return;
      }

      dispatch(setConveyorAccessToken(''));
      aoiAlert(t('notification.success.autoReleaseConveyorControl'), ALERT_TYPES.COMMON_INFO);
    }

    if (!_.isEmpty(cameraAccessToken)) {
      // auto release camera
      const cameraRes = await releaseCameraControl(cameraAccessToken);

      if (cameraRes.error) {
        aoiAlert(t('notification.error.releaseCameraControl'), ALERT_TYPES.COMMON_ERROR);
        console.error('releaseCameraControl error:', _.get(cameraRes, 'error.message', ''));
        return;
      }

      dispatch(setCameraAccessToken(''));
    }

    dispatch(setIsProgrammingUsingConveyor(false));
    dispatch(setCurrentControlledConveyorSlotId(null));
  };

  const handleUpdateProduct = async (
    curProduct,
    productName,
    panelWidth,
    panelHeight,
    arrayRow,
    arrayColumn,
    conveyorWidth,
    currentStep,
  ) => {
    if (_.isUndefined(curProduct)) {
      // create a new product
      const res = await addProduct({product_name: productName, description: ''});
      if (res.error) {
        if (res.error.status === COMMON_HTTP_CODE.conflict) {
          console.error('product name exist');
          aoiAlert(t('notification.error.nameExists'), ALERT_TYPES.COMMON_ERROR);
          return;
        }
        console.error('addProduct error:', _.get(res, 'error.message', ''));
        aoiAlert(t('notification.error.addProduct'), ALERT_TYPES.COMMON_ERROR);
        return;
      }

      const res1 = await updateProduct({
        product_id: Number(_.get(res, 'data.product_id', 0)),
        board_width_mm: panelWidth,
        board_height_mm: panelHeight,
        conveyor_width_mm: conveyorWidth,
        product_name: productName,
        // TODO: add array_row and array_column
      });

      if (res1.error) {
        aoiAlert(t('notification.error.updateProduct'), ALERT_TYPES.COMMON_ERROR);
        console.error('updateProduct error:', _.get(res, 'error.message', ''));
        return;
      }

      navigate(`/auto-programming-setup?product-id=${_.get(res, 'data.product_id', '')}`);

      aoiAlert(t('notification.success.changesSaved'), ALERT_TYPES.COMMON_INFO);

      setCurrentStep(currentStep + 1);

      return;
    } else {
      // update
      const res = await updateProduct({
        product_id: Number(curProduct.product_id),
        board_width_mm: panelWidth,
        board_height_mm: panelHeight,
        conveyor_width_mm: conveyorWidth,
        product_name: productName,
        // TODO: add array_row and array_column
      });

      if (res.error) {
        aoiAlert(t('notification.error.updateProduct'), ALERT_TYPES.COMMON_ERROR);
        console.error('updateProduct error:', _.get(res, 'error.message', ''));
        return;
      }

      aoiAlert(t('notification.success.changesSaved'), ALERT_TYPES.COMMON_INFO);

      await refetchCurProduct();

      setCurrentStep(currentStep + 1);
    }
  };

  const handleRunFullAutoProgramming = async (
    autoProgramInspectionRegion,
    selectedInspectionTypes,
    productId,
    step,
  ) => {
    dispatch(setIsContainerLvlLoadingEnabled(true));
    dispatch(setContainerLvlLoadingMsg(t('loader.autoProgramming')));

    const res = await runFullAutoProgram({
      product_id: Number(productId),
      config: {
        component_for_line_item: selectedInspectionTypes,
      },
      step,
      roi: {
        type: 'obb',
        points: [
          {
            x: _.round(_.get(autoProgramInspectionRegion, 'pmin.x', 0), 0),
            y: _.round(_.get(autoProgramInspectionRegion, 'pmin.y', 0), 0),
          },
          {
            x: _.round(_.get(autoProgramInspectionRegion, 'pmax.x', 0), 0) - 1,
            y: _.round(_.get(autoProgramInspectionRegion, 'pmax.y', 0), 0) - 1,
          }
        ],
        center: null,
        angle: 0,
      }
    });

    if (res.error) {
      aoiAlert(t('notification.error.runFullAutoProgramming'), ALERT_TYPES.COMMON_ERROR);
      console.error('runFullAutoProgramming error:', _.get(res, 'error.message', ''));
      dispatch(setIsContainerLvlLoadingEnabled(false));
      dispatch(setContainerLvlLoadingMsg(''));
      return;
    }

    dispatch(setIsContainerLvlLoadingEnabled(false));
    dispatch(setContainerLvlLoadingMsg(''));

    // dispatch(setShouldRunReevaluateAfterRetrain({ productId: Number(productId), shouldRun: true}));
    // await handleAutoGenerateAgentParams(productId);

    navigate(`/teach?product-id=${productId}&from-auto-programming=true`);
  };

  useEffect(() => {
    if (!_.isEmpty(conveyorAccessToken) && _.includes([0, 4], currentStep)) {
      // user leaves conveyor required step so release the conveyor control
      const cameraAccessToken = _.get(store.getState(), 'setting.cameraAccessToken', '');

      releaseConveyorAndCamera(conveyorAccessToken, cameraAccessToken);
    }
  }, [currentStep]);

  useEffect(() => {
    if (_.isObject(curProduct)) {
      setProductName(_.get(curProduct, 'product_name', ''));
      setPanelWidth(_.get(curProduct, 'board_width_mm', 1));
      setPanelHeight(_.get(curProduct, 'board_height_mm', 1));
      // TODO: update this two
      // setArrayRow(_.get(curProduct, 'array_row', 1));
      // setArrayColumn(_.get(curProduct, 'array_column', 1));
      setConveyorWidth(_.get(curProduct, 'conveyor_width_mm', 50));
    } else {
      setProductName('');
      setPanelWidth(1);
      setPanelHeight(1);
      // setArrayRow(1);
      // setArrayColumn(1);
      setConveyorWidth(50);
    }
  }, [curProduct]);

  useEffect(() => {
    if (!autoProgrammingConfigTemplate) return;

    // remove TEXT from default autoProgrammingConfigTemplate since ocr performance is not good for now
    const cloned = _.cloneDeep(autoProgrammingConfigTemplate.component_for_line_item);
    // delete cloned['TEXT'];
    cloned['TEXT'] = [];
    setSelectedInspectionTypes(cloned);
  }, [autoProgrammingConfigTemplate]);

  useEffect(() => {
    const initDimension = async () => {
      await sleep(100);

      setChildrenContainerDimension({
        width: childrenContainerRef.current.offsetWidth,
        height: childrenContainerRef.current.offsetHeight,
      });
    };
    initDimension();

    refetchAutoProgrammingConfigTemplate();
    refetchCurProduct();

    return () => {
      const conveyorAccessToken = _.get(store.getState(), 'setting.conveyorAccessToken', '');
      const cameraAccessToken = _.get(store.getState(), 'setting.cameraAccessToken', '');
      releaseConveyorAndCamera(
        conveyorAccessToken,
        cameraAccessToken
      );
    };
  }, []);

  return (
    <div className='flex flex-1 py-[48px] px-[140px] justify-center items-center self-stretch'>
      <div className='flex w-[289px] p-6 flex-col gap-6 self-stretch'>
        <div className='flex flex-col gap-3 self-stretch'>
          <img
            src='/icn/wand_blue.svg'
            alt='wand_blue'
            className='w-6 h-6'
          />
          <span className='font-source text-[18px] font-normal leading-[normal]'>
            {t('autoProgramming.quickTeachProgramming')}
          </span>
          <span className='font-source text-[12px] font-normal leading-[150%]'>
            {t('autoProgramming.autoGenerateRois')}
          </span>
        </div>
        <ConfigProvider
          theme={{
            components: {
              Steps: {
                colorPrimary: '#1890FF'
              }
            }
          }}
        >
          <Steps
            direction='vertical'
            current={currentStep}
            items={[
              {
                title: <span className='font-source text-[14px] font-normal'>
                  {t('autoProgramming.basicInfo')}
                </span>,
              },
              {
              title: <span className='font-source text-[14px] font-normal'>
                  {t('autoProgramming.placePCBAOnConveyor')}
                </span>
              },
              {
                title: <span className='font-source text-[14px] font-normal'>
                  {t('autoProgramming.getDimension')}
                </span>
              },
              {
                title: <span className='font-source text-[14px] font-normal'>
                  {t('autoProgramming.capture')}
                </span>
              },
              {
                title: <span className='font-source text-[14px] font-normal'>
                  {t('autoProgramming.addCAD')}
                </span>
              }
            ]}
          />
        </ConfigProvider>
        {currentStep === 2 &&
          <Collapse
            defaultActiveKey={['1']}
            items={[
              {
                key: '1',
                label: <span className='font-source text-[12px] font-normal leading-[150%] text-gray-6'>
                  {`${t('productDefine.instructions')}:`}
                </span>,
                children: <div className='flex flex-col self-stretch'>
                  <div className='flex py-4 px-2'>
                    <img src='/img/PCBDimensionInstruction_color.svg' alt='PCBDimensionInstruction' className='w-[100%] h-[100%]' />
                  </div>
                  <div
                    className='flex p-2 flex-col self-stretch'
                  >
                    <h1 className='list-item font-source text-[12px] font-normal leading-[150%] text-gray-6 list-disc ml-[1rem]'>
                      {t('productDefine.instructionStep1')}
                    </h1>
                    <h1 className='font-source text-[12px] font-normal leading-[150%] text-gray-6 list-item list-disc ml-[1rem]'>
                      {t('productDefine.instructionStep2')}
                    </h1>
                    <h1 className='font-source text-[12px] font-normal leading-[150%] text-gray-6 list-item list-disc ml-[1rem]'>
                      {t('productDefine.instructionStep3')}
                    </h1>
                    <h1 className='font-source text-[12px] font-normal leading-[150%] text-gray-6 list-item list-disc ml-[1rem]'>
                      {t('productDefine.instructionStep4')}
                    </h1>
                  </div>
                </div>,
              }
            ]}
          />
        }
      </div>
      <div className='flex flex-1 self-stretch'>
        <div className='flex rounded-[8px] bg-[#ffffff08] shadow-[0px_4px_4px_rgba(0,0,0,0.25)] flex-1 flex-col'>
          <div
            className='flex flex-1 self-stretch'
            ref={childrenContainerRef}
          >
            <div
              className='flex overflow-y-auto'
              style={{
                width: childrenContainerDimension?.width,
                height: childrenContainerDimension?.height,
              }}
            >
              {currentStep === 0 &&
                <BasicInfo
                  arrayRow={arrayRow}
                  curProduct={curProduct}
                  productName={productName}
                  panelWidth={panelWidth}
                  panelHeight={panelHeight}
                  arrayColumn={arrayColumn}
                  setProductName={setProductName}
                  setPanelWidth={setPanelWidth}
                  setPanelHeight={setPanelHeight}
                  setArrayRow={setArrayRow}
                  setArrayColumn={setArrayColumn}
                  autoProgrammingConfigTemplate={autoProgrammingConfigTemplate}
                  selectedInspectionTypes={selectedInspectionTypes}
                  setSelectedInspectionTypes={setSelectedInspectionTypes}
                />
              }
              {currentStep === 1 &&
                <ConveyorSetup
                  curProduct={curProduct}
                  conveyorWidth={conveyorWidth}
                  setConveyorWidth={setConveyorWidth}
                />
              }
              {currentStep === 2 &&
                <PCBDimension
                  panelHeight={panelHeight}
                  panelWidth={panelWidth}
                  setPanelHeight={setPanelHeight}
                  setPanelWidth={setPanelWidth}
                  curProduct={curProduct}
                  refetchCurProduct={refetchCurProduct}
                />
              }
              {currentStep === 3 &&
                <FullCapture
                  curProduct={curProduct}
                  refetchCurProduct={refetchCurProduct}
                  setAutoProgramInspectionRegion={setAutoProgramInspectionRegion}
                  autoProgramInspectionRegion={autoProgramInspectionRegion}
                />
              }
              {currentStep === 4 &&
                <UploadCAD
                  curProduct={curProduct}
                  currentStep={currentAddCADStep}
                  setCurrentStep={setCurrentAddCADStep}
                  selectedInspectionTypes={selectedInspectionTypes}
                  autoProgramInspectionRegion={autoProgramInspectionRegion}
                />
              }
            </div>
          </div>
          <div className='flex items-center justify-between p-4 self-stretch'>
            <Button
              onClick={() => {
                navigate('/home');
              }}
            >
              <span className='font-source text-[14px] font-semibold leading-[150%]'>
                {t('autoProgramming.exitTeach')}
              </span>
            </Button>
            <div className='flex items-center gap-2 self-stretch'>
             {currentStep !== 4 && 
             <Button
                disabled={currentStep === 0 }
                onClick={() => setCurrentStep(currentStep - 1)}
              >
                <span className='font-source text-[14px] font-semibold leading-[150%]'>
                  {t('common.previous')}
                </span>
              </Button>}
              {currentStep === 4 && currentAddCADStep === -1 ?
                <Fragment>
                  <Button
                    onClick={() => {
                      navigate(`/teach?product-id=${productId}&from-auto-programming=true`);
                    }}
                  >
                    <span className='font-source text-[14px] font-semibold leading-[150%]'>
                      {t('autoProgramming.manualProgramming')}
                    </span>
                  </Button>
                  <Button
                    type='primary'
                    onClick={() => {
                      handleRunFullAutoProgramming(
                        autoProgramInspectionRegion,
                        selectedInspectionTypes,
                        productId,
                        0,
                      );
                    }}
                  >
                    <span className='font-source text-[14px] font-semibold leading-[150%]'>
                      {t('autoProgramming.runAutoProgram')}
                    </span>
                  </Button>
                </Fragment>
                : currentStep === 4 && currentAddCADStep === 0 ? (
                  <Button
                    onClick={() => {
                      setCurrentAddCADStep(-1);
                    }}
                  >
                    <span className='font-source text-[14px] font-semibold leading-[150%]'>
                      {t('common.exitUploadCAD')}
                    </span>
                  </Button>
              ) :
                <Button
                  type='primary'
                  disabled={
                    currentStep === 4 ||
                    (currentStep === 1 && _.isEmpty(conveyorAccessToken))
                  }
                  onClick={() => {
                    if (
                      currentStep === 3 &&
                      (
                        _.isEmpty(autoProgramInspectionRegion) ||
                        _.get(autoProgramInspectionRegion, 'pmin.x', 0) >= _.get(autoProgramInspectionRegion, 'pmax.x', 0) ||
                        _.get(autoProgramInspectionRegion, 'pmin.y', 0) >= _.get(autoProgramInspectionRegion, 'pmax.y', 0)
                      )
                    ) {
                      aoiAlert(t('notification.error.selectInspectionRegion'), ALERT_TYPES.COMMON_ERROR);
                      return;
                    }

                    const run = async (
                      curProduct,
                      productName,
                      panelWidth,
                      panelHeight,
                      arrayRow,
                      arrayColumn,
                      conveyorWidth,
                      currentStep,
                    ) => {
                      await handleUpdateProduct(
                        curProduct,
                        productName,
                        panelWidth,
                        panelHeight,
                        arrayRow,
                        arrayColumn,
                        conveyorWidth,
                        currentStep,
                      );
                    };

                    run(
                      curProduct,
                      productName,
                      panelWidth,
                      panelHeight,
                      arrayRow,
                      arrayColumn,
                      conveyorWidth,
                      currentStep,
                    );
                  }}
                >
                  <span className='font-source text-[14px] font-semibold leading-[150%]'>
                    {t('common.next')}
                  </span>
                </Button>
              }
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AutoProgramming;