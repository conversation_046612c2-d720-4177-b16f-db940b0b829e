import React, { useState } from 'react';
import { CustomSegmented } from '../../../../common/styledComponent';
import { Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';
import { isAOI2DSMT } from '../../../../common/const';
import PCBArrayViewer from '../../../../viewer/PCBArrayViewer';


const Display = (props) => {
  const {
    curProduct,
    allComponents,
    selectedTool,
    setSelectedTool,
    handleFinishDrawingSelectionRoi,
    arrayRegisteration,
    handleRefetchAllComponents,
    refetchArrayRegisteration,
    setCurSelectedMarker,
    curSelectedMarker,
    curMarkers,
    curPairModeMarkers,
    isSubBoardSelectionRoiDisplayed,
    panZoomToArrayBoardSelectionIdx,
    setPanZoomToArrayBoardSelectionIdx,
  } = props;

  const { t } = useTranslation();

  return (
    <div className='relative w-full h-full'>
      {/* scene starts */}
      <div className='absolute w-full h-full z-[10] top-0 left-0'>
        <PCBArrayViewer
          curProduct={curProduct}
          selectedTool={selectedTool}
          allComponents={allComponents}
          handleFinishDrawingSelectionRoi={handleFinishDrawingSelectionRoi}
          arrayRegisteration={arrayRegisteration}
          handleRefetchAllComponents={handleRefetchAllComponents}
          refetchArrayRegisteration={refetchArrayRegisteration}
          setCurSelectedMarker={setCurSelectedMarker}
          curSelectedMarker={curSelectedMarker}
          curMarkers={curMarkers}
          curPairModeMarkers={curPairModeMarkers}
          setSelectedTool={setSelectedTool}
          isSubBoardSelectionRoiDisplayed={isSubBoardSelectionRoiDisplayed}
          panZoomToArrayBoardSelectionIdx={panZoomToArrayBoardSelectionIdx}
          setPanZoomToArrayBoardSelectionIdx={setPanZoomToArrayBoardSelectionIdx}
        />
      </div>
      {/* scene ends */}
      {/* tools & controllers start */}
      <div
        className='absolute right-[8px] z-[20]'
        style={{ top: 'calc(50% - 68px)' }}
      >
        <CustomSegmented
          vertical
          value={selectedTool}
          onChange={(value) => {
            if (value === 'divider') return;
            setSelectedTool(value);
          }}
          options={[
            {
              value: 'select',
              label: <Tooltip
                title={<span className='font-source text-[12px] font-semibold leading-[150%]'>{t('productDefine.select')}</span>}
                placement='left'
              >
                <div className='flex w-8 h-8 justify-center items-center'>
                  <img
                    src='/icn/navigator_white.svg'
                    alt='navigator'
                    className='w-3 h-3'
                  />
                </div>
              </Tooltip>,
            },
            {
              value: 'transform',
              label: <Tooltip
                title={<span className='font-source text-[12px] font-semibold leading-[150%]'>
                  {t('productDefine.panZoom')}
                </span>}
                placement='left'
              >
                <div className='flex w-8 h-8 justify-center items-center'>
                  <img
                    src='/icn/backHand_white.svg'
                    alt='locator'
                    className='w-4 h-4'
                  />
                </div>
              </Tooltip>,
            },
            ...(!isAOI2DSMT ? [{
              value: 'defineSelectionRoi',
              label: <Tooltip
                title={<span className='font-source text-[12px] font-semibold leading-[150%]'>
                  {t('productDefine.defineSelectionRoi')}
                </span>}
                placement='left'
              >
                <div className='flex w-8 h-8 justify-center items-center'>
                  <img
                    src='/icn/viewIn3D_white.svg'
                    alt='viewIn3D'
                    className='w-4 h-4'
                  />
                </div>
              </Tooltip>,
            }] : [])
          ]}
        />
      </div>
    </div>
  );
};

export default Display;