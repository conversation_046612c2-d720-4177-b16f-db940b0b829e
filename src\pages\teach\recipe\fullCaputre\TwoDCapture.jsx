import { Button, InputN<PERSON>ber, Slider } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import _ from 'lodash';


const TwoDCapture = (props) => {
  const {
    d2CaptureSettings,
    setD2CaptureSettings,
  } = props;

  const { t } = useTranslation();

  return (
    <div className='flex py-2 flex-col self-stretch px-4 gap-1 bg-[#ffffff0d]'>
      <div className='grid grid-cols-3 gap-1 items-center self-stretch grid-cols-[90px_1fr_60px]'>
        <div className="flex py-1 items-center flex-1 self-stretch">
          <span className="text-xs whitespace-nowrap">
            {t('productDefine.exposureTime')}
          </span>
        </div>
        <div className='flex items-center gap-0.5 flex-1 self-stretch'>
          <Slider
            style={{ width: '100%' }}
            min={1}
            step={1}
            max={100}
            value={_.get(
              d2CaptureSettings,
              'exposure_time'
            )}
            onChange={(value) => {
              let newD2CaptureSettings = _.cloneDeep(d2CaptureSettings);
              newD2CaptureSettings.exposure_time = value;
              setD2CaptureSettings(newD2CaptureSettings);
            }}
          />
        </div>
        <div className="flex items-center gap-0.5 flex-1 self-stretch">
          <InputNumber
            controls={false}
            size="small"
            value={_.get(
              d2CaptureSettings,
              'exposure_time'
            )}
            onChange={(value) => {
              let newD2CaptureSettings = _.cloneDeep(d2CaptureSettings);
              newD2CaptureSettings.exposure_time = value;
              setD2CaptureSettings(newD2CaptureSettings);
            }}
            min={1}
            max={100}
            step={1}
          />
        </div>
      </div>
      <div className='grid grid-cols-3 gap-1 items-center self-stretch grid-cols-[90px_1fr_60px]'>
        <div className="flex py-1 items-center flex-1 self-stretch">
          <span className="text-xs whitespace-nowrap">
            {t('productDefine.gain')}
          </span>
        </div>
        <div className='flex items-center gap-0.5 flex-1 self-stretch'>
          <Slider
            style={{ width: '100%' }}
            min={0}
            max={2}
            step={1}
            value={_.get(
              d2CaptureSettings,
              'gain'
            )}
            onChange={(value) => {
              let newD2CaptureSettings = _.cloneDeep(d2CaptureSettings);
              newD2CaptureSettings.gain = value;
              setD2CaptureSettings(newD2CaptureSettings);
            }}
          />
        </div>
        <div className="flex items-center gap-0.5 flex-1 self-stretch">
          <InputNumber
            controls={false}
            size="small"
            value={_.get(
              d2CaptureSettings,
              'gain'
            )}
            onChange={(value) => {
              let newD2CaptureSettings = _.cloneDeep(d2CaptureSettings);
              newD2CaptureSettings.gain = value;
              setD2CaptureSettings(newD2CaptureSettings);
            }}
            min={0}
            max={2}
            step={1}
          />
        </div>
      </div>
      {/* <div className='flex py-3 gap-2.5 items-center self-stretch'>
        <Button
          style={{ width: '50%' }}
        >
          <span className='font-source text-[12px] font-normal leading-[150%]'>
            {t('productDefine.resetToDefault')}
          </span>
        </Button>
        <Button
          style={{ width: '50%' }}
        >
          <span className='font-source text-[12px] font-normal leading-[150%]'>
            {t('productDefine.quick2DCapture')}
          </span>
        </Button>
      </div> */}
    </div>
  );
};

export default TwoDCapture;