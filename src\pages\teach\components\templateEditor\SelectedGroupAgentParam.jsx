import { Checkbox, Collapse, ConfigProvider } from 'antd';
import React, { Fragment, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useUpdateAgentParamsMutation, useUpdateComponentMutation, useUpdateFeatureMutation } from '../../../../services/product';
import _ from 'lodash';
import { ALERT_TYPES, aoiAlert } from '../../../../common/alert';
import AgentParams from './AgentParams';
import GroupAgentParams from './GroupAgentParams';
import { featureTypeToAgentNames } from '../../../../common/const';


const SelectedGroupAgentParam = (props) => {
  const {
    setSelectedAgentParam,
    refetchAllComponents,
    updateAllFeaturesState,
    selectedFeatureType,
    selectedCid,
    selectedPartNo,
    selectedPackageNo,
    allComponents,
    goldenProductId,
    selectedScope,
    allFeatures,
    selectedGroupFeatureTypeAgentParams,
    handleRefetchSelectedGroupAgentParams,
    setSelectedGroupFeatureTypeAgentParams,
    shouldFeaturesRerender,
    shouldComponentsRerender,
  } = props;
  
  const { t } = useTranslation();
  
  const [updateComponent] = useUpdateComponentMutation();
  const [updateAgentParamsInGroup] = useUpdateAgentParamsMutation();

  if (!selectedGroupFeatureTypeAgentParams) return;

  // console.log('selectedGroupFeatureTypeAgentParams', selectedGroupFeatureTypeAgentParams);

  return (
    <Fragment>
      {_.map(
        _.filter(_.keys(selectedGroupFeatureTypeAgentParams.line_item_params),
        k => _.includes(_.get(featureTypeToAgentNames, _.startsWith(selectedFeatureType, '_text') ? '_text' : selectedFeatureType, []), k)),
        (agentKey, id) => (
        <ConfigProvider
          theme={{
            components: {
              Collapse: {
                headerBg: '#00000033',
              }
            }
          }}
        >
          <Collapse
            style={{ width: '100%' }}
            defaultActiveKey={[agentKey]}
            items={[
              {
                key: agentKey,
                label: <div className='flex flex-1 justify-between items-center'>
                  <span className='font-source text-[12px] font-semibold leading-[normal] pt-0.5'>
                    {t(`lineItemName.${agentKey}`)}
                  </span>
                  <Checkbox
                    checked={_.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${agentKey}.enabled`, false)}
                    onClick={(e) => {
                      e.stopPropagation();
                      let newGroupObj = _.cloneDeep(selectedGroupFeatureTypeAgentParams);
                      newGroupObj = _.set(newGroupObj, `line_item_params.${agentKey}.enabled`, !_.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${agentKey}.enabled`, false));

                      const submit = async (
                        newGroupObj,
                        selectedScope,
                        goldenProductId,
                        selectedCid,
                        selectedPartNo,
                        selectedPackageNo,
                        selectedFeatureType,
                        allFeatures,
                      ) => {
                        const payload = {
                          line_item_params: newGroupObj.line_item_params,
                          product_id: goldenProductId,
                          step: 0,
                          feature_type: selectedFeatureType,
                        };

                        if (_.isInteger(selectedCid) && selectedScope === 'component') {
                          payload.component_id = selectedCid;
                        } else if (!_.isEmpty(selectedPartNo) && selectedScope === 'part') {
                          payload.part_no = selectedPartNo;
                        } else if (!_.isEmpty(selectedPackageNo) && selectedScope === 'package') {
                          payload.package_no = selectedPackageNo;
                        }

                        const res = await updateAgentParamsInGroup(payload);

                        if (res.error) {
                          aoiAlert(t('notification.error.updateFeature'), ALERT_TYPES.COMMON_ERROR);
                          console.error('update feature failed', res.error.message);
                          return;
                        }

                        const relatedFeatures = _.filter(
                          allFeatures,
                          f => {
                            if (_.isInteger(selectedCid) && selectedScope === 'component') {
                              return f.group_id === selectedCid && f.feature_type === selectedFeatureType;
                            } else if (!_.isEmpty(selectedPartNo) && selectedScope === 'part') {
                              return f.part_no === selectedPartNo && f.feature_type === selectedFeatureType;
                            } else if (!_.isEmpty(selectedPackageNo) && selectedScope === 'package') {
                              return f.package_no === selectedPackageNo && f.feature_type === selectedFeatureType;
                            }
                          }
                        );

                        if (relatedFeatures.length === 0) return;

                        await updateAllFeaturesState(_.map(relatedFeatures, f => f.feature_id), 'update', _.map(relatedFeatures, f => ({
                          ...f,
                          line_item_params: newGroupObj.line_item_params,
                        })));

                        await handleRefetchSelectedGroupAgentParams(
                          selectedFeatureType,
                          selectedCid,
                          selectedPartNo,
                          selectedPackageNo,
                          goldenProductId,
                          selectedScope,
                        );
                      };

                      submit(
                        newGroupObj,
                        selectedScope,
                        goldenProductId,
                        selectedCid,
                        selectedPartNo,
                        selectedPackageNo,
                        selectedFeatureType,
                        allFeatures,
                      );
                    }}
                  />
                </div>,
                children: <GroupAgentParams
                  selectedGroupFeatureTypeAgentParams={selectedGroupFeatureTypeAgentParams}
                  agentObj={_.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${agentKey}`, {})}
                  updateAllFeaturesState={updateAllFeaturesState}
                  lintItemName={agentKey}
                  setSelectedAgentParam={setSelectedAgentParam}
                  allFeatures={allFeatures}
                  refetchAllComponents={refetchAllComponents}
                  updateComponent={updateComponent}
                  selectedCid={selectedCid}
                  selectedPartNo={selectedPartNo}
                  selectedPackageNo={selectedPackageNo}
                  selectedScope={selectedScope}
                  handleRefetchSelectedGroupAgentParams={handleRefetchSelectedGroupAgentParams}
                  goldenProductId={goldenProductId}
                  selectedFeatureType={selectedFeatureType}
                  setSelectedGroupFeatureTypeAgentParams={setSelectedGroupFeatureTypeAgentParams}
                  allComponents={allComponents}
                  shouldFeaturesRerender={shouldFeaturesRerender}
                  shouldComponentsRerender={shouldComponentsRerender}
                />
              }
            ]}
          />
        </ConfigProvider>
      ))}
    </Fragment>
  );
};

export default SelectedGroupAgentParam;