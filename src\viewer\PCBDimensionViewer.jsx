import React, { useCallback, useEffect, useRef, useState } from 'react';
import { getRawViewportDimension, loadHighResolScene, loadInitFullSizeThumbnail, zoomPanToObject } from './util';
import _ from 'lodash';
import { fabric } from 'fabric';
import { useGetImageMetaDataQuery } from '../services/camera';
import { useDispatch } from 'react-redux';
import { setContainerLvlLoadingMsg, setIsContainerLvlLoadingEnabled, setTransparentLoadingEnabled } from '../reducer/setting';
import { useTranslation } from 'react-i18next';
import { ALERT_TYPES, aoiAlert } from '../common/alert';
import { defaultDashedArray, highResoluRefreshInterval, viewportMouseMoveUpdateSupportLineInterval, viewportSupportLineDefaultColor, viewportSupportLineDefaultStrokeWidth } from '../common/const';
import { destroyFabricCanvas } from '../common/util';


const PCBDimensionViewer = (props) => {
  const {
    imageUri,
    thumbnailUri,
    depthImgUri,
    // rawImageW,
    // rawImageH,
    viewerContainerRef,
    selectedTool,
    setSelectedTool,
    curProduct,
    refetchCurProduct,
    setCurTopRightPos,
    setCurBottemLeftPos,
    handlePickPointUpdateCameraPos,
    cameraPosition,
  } = props;

  const dispatch = useDispatch();
  const { t } = useTranslation();

  const canvasElRef = useRef();
  const fcanvasRef = useRef();
  const displayedHighResolSceneRef = useRef(); // constanly reload when zooming in/out and paning
  const thumbnailBgSceneRef = useRef();
  const resizeHandlerRef = useRef();
  const containerResizeObserverRef = useRef();
  const isLocked = useRef(false);
  const isPanning = useRef(false);
  const mode = useRef('transform'); // transform, selectBottomLeftPixel, selectTopRightPixel
  const viewerInitialized = useRef(false);
  const cameraPosRef = useRef();
  const horizontalSupportLine = useRef();
  const verticalSupportLine = useRef();
  const prevViewportDimension = useRef();

  const [selectDimensionMousePos, setSelectDimensionMousePos] = useState({
    curMouseTop: 0,
    curMouseLeft: 0,
    curX: 0,
    curY: 0,
  });

  const { data: curImageMetaData } = useGetImageMetaDataQuery({ uri: imageUri });

  const updateZIndex = () => {
    // bigger z-index called later
    if (thumbnailBgSceneRef.current) thumbnailBgSceneRef.current.moveTo(1);
    if (displayedHighResolSceneRef.current) {
      displayedHighResolSceneRef.current.moveTo(2);
    }
    if (horizontalSupportLine.current) horizontalSupportLine.current.moveTo(3);
    if (verticalSupportLine.current) verticalSupportLine.current.moveTo(4);
    fcanvasRef.current.requestRenderAll();
  };

  const delayUpdateSupportLine = useCallback(
    _.debounce((
      fcanvasRef,
      rawImageW,
      rawImageH,
      pointer,
      zoom,
    ) => {
      const {
        left,
        top,
        bottom,
        right
      } = getRawViewportDimension(fcanvasRef, rawImageW, rawImageH);

      if (horizontalSupportLine.current) {
        // horizontalSupportLine.current.set({ x1: left, x2: right, y1: top, y2: top });
        horizontalSupportLine.current.set({ x1: left, x2: right, y1: pointer.y, y2: pointer.y });
        // also update the storke width based on zoom
        horizontalSupportLine.current.set({ strokeWidth: viewportSupportLineDefaultStrokeWidth / zoom * 3 });
      }
      if (verticalSupportLine.current) {
        verticalSupportLine.current.set({ x1: pointer.x, x2: pointer.x, y1: top, y2: bottom });
        // also update the storke width based on zoom
        verticalSupportLine.current.set({ strokeWidth: viewportSupportLineDefaultStrokeWidth / zoom * 3 });
      }

      updateZIndex();
    }, highResoluRefreshInterval)
  , [curImageMetaData]);

  // const delayUpdateSupportLineByMouseMove = useCallback(
  //   _.debounce((
  //     fcanvasRef,
  //     opt
  //   ) => {
  //     const pointer = fcanvasRef.current.getPointer(opt.e);

  //     if (horizontalSupportLine.current) {
  //       horizontalSupportLine.current.set({ y1: pointer.y, y2: pointer.y });
  //     }
  //     if (verticalSupportLine.current) {
  //       verticalSupportLine.current.set({ x1: pointer.x, x2: pointer.x });
  //     }

  //     updateZIndex();
  //   }, viewportMouseMoveUpdateSupportLineInterval)
  // , []);

  const delayLoadHighSoluScene = useCallback(
    _.debounce(async ({
      fcanvasRef,
      rawImageW,
      rawImageH,
      displayedHighResolSceneRef,
      imageUri,
      depthUri,
    }) => {
      dispatch(setTransparentLoadingEnabled(true));

      await loadHighResolScene({
        fcanvasRef,
        rawImageW,
        rawImageH,
        displayedHighResolSceneRef,
        imageUri,
        depthUri,
        type: 'image',
        callback: () => {
          updateZIndex();
        },
      });

      dispatch(setTransparentLoadingEnabled(false));
    }, highResoluRefreshInterval),
  [curImageMetaData]);

  const updateCameraPos = useCallback(
    (type, curCamPos) => {
      handlePickPointUpdateCameraPos(type, curCamPos);
    },
  [cameraPosition]);

  const resetView = () => {
    if (!thumbnailBgSceneRef.current || !fcanvasRef.current) return;

    zoomPanToObject(thumbnailBgSceneRef.current, fcanvasRef.current, 0.01);
  };

  useEffect(() => {
    mode.current = selectedTool;
    if (!fcanvasRef.current) return;
    // fcanvasRef.current.hoverCursor = selectedTool === 'transform' ? 'default' : 'crosshair';
    fcanvasRef.current.defaultCursor = selectedTool === 'transform' ? 'default' : 'crosshair';

    if (horizontalSupportLine.current) {
      fcanvasRef.current.remove(horizontalSupportLine.current);
      horizontalSupportLine.current = null;
    }
    if (verticalSupportLine.current) {
      fcanvasRef.current.remove(verticalSupportLine.current);
      verticalSupportLine.current = null;
    }

    if (selectedTool !== 'transform') {
      // init support line based on current mouse position
      const {
        left,
        top,
        bottom,
        right
      } = getRawViewportDimension(fcanvasRef, _.get(curImageMetaData, 'width'), _.get(curImageMetaData, 'height'));

      const zoom = fcanvasRef.current.getZoom();

      horizontalSupportLine.current = new fabric.Line([
        left,
        top,
        right,
        top
      ], {
        strokeDashArray: defaultDashedArray,
        stroke: viewportSupportLineDefaultColor,
        strokeWidth: viewportSupportLineDefaultStrokeWidth / zoom * 3,
        evented: false,
        selectable: false,
      });
      verticalSupportLine.current = new fabric.Line([
        left,
        top,
        left,
        bottom
      ], {
        strokeDashArray: defaultDashedArray,
        stroke: viewportSupportLineDefaultColor,
        strokeWidth: viewportSupportLineDefaultStrokeWidth / zoom * 3,
        evented: false,
        selectable: false,
      });
      fcanvasRef.current.add(horizontalSupportLine.current);
      fcanvasRef.current.add(verticalSupportLine.current);
    }

    updateZIndex();
  }, [selectedTool]);

  useEffect(() => {
    if (_.isEmpty(curImageMetaData)) return;

    if (!viewerContainerRef.current) return;

    if (!viewerInitialized.current) {
      const fcanvas = new fabric.Canvas(canvasElRef.current, {
        antialias: 'off',
        uniformScaling: false,
        fireMiddleClick: true,
        fireRightClick: true,
        stopContextMenu: true,
      });
  
      fcanvasRef.current = fcanvas;

      fcanvas.setWidth(viewerContainerRef.current.offsetWidth);
      fcanvas.setHeight(viewerContainerRef.current.offsetHeight);

      viewerInitialized.current = true;
    }

    const init = async (curImageMetaData, imageUri, thumbnailUri, depthImgUri) => {
      if (!viewerContainerRef.current || !fcanvasRef.current) return;

      fcanvasRef.current.off('mouse:down');
      fcanvasRef.current.off('mouse:move');
      fcanvasRef.current.off('mouse:up');
      fcanvasRef.current.off('mouse:wheel');

      // attach mouse event for zooming and panning
      fcanvasRef.current.on('mouse:down', (opt) => {
        if (isLocked.current) return;

        if (mode.current === 'transform') {
          if (opt.target) {
            isPanning.current = false;
            return;
          }
          
          isPanning.current = true;
          fcanvasRef.current.selection = false;
          fcanvasRef.current.setCursor('grab');
        } else if (opt.e.button === 1) {
          isPanning.current = true;
          fcanvasRef.current.selection = false;
          fcanvasRef.current.setCursor('grab');
        } else if (opt.e.button === 2) {
          setSelectedTool('transform');
        }
      });

      fcanvasRef.current.on('mouse:move', (opt) => {
        if (isLocked.current) return;

        // if (mode.current === 'transform') {
        if (isPanning.current) {
          if (isPanning.current && opt && opt.e) {
            fcanvasRef.current.setCursor('grab');
            const delta = new fabric.Point(opt.e.movementX, opt.e.movementY);

            fcanvasRef.current.relativePan(delta);
          }
        } else if (_.includes(['selectBottomLeftPixel', 'selectTopRightPixel'], mode.current)) {
          const pointer = fcanvasRef.current.getPointer(opt.e);
          const x = Math.floor(pointer.x);
          const y = Math.floor(pointer.y);

          const mousePos = fcanvasRef.current.getPointer(opt.e, true);

          // fabricjs's origin is top-left and y-axis is pointing downwards, but user and backend's origin is bottom-left and y-axis is pointing upwards
          setSelectDimensionMousePos({
            curMouseTop: mousePos.y,
            curMouseLeft: mousePos.x,
            curX: x,
            curY: y,
          });

          if (horizontalSupportLine.current) {
            horizontalSupportLine.current.set({ y1: pointer.y, y2: pointer.y });
          }
          if (verticalSupportLine.current) {
            verticalSupportLine.current.set({ x1: pointer.x, x2: pointer.x });
          }

          updateZIndex();
        }

        // delayUpdateSupportLineByMouseMove(fcanvasRef, opt);
      });

      fcanvasRef.current.on('mouse:up', async (opt) => {
        if (isLocked.current) return;

        if (isPanning.current) {
          isPanning.current = false;
          fcanvasRef.current.setCursor('default');

          // reload high resol scene
          if (_.isString(imageUri)) {
            // delayLoadHighSoluScene({
            //   fcanvasRef,
            //   rawImageW: _.get(curImageMetaData, 'width'),
            //   rawImageH: _.get(curImageMetaData, 'height'),
            //   displayedHighResolSceneRef,
            //   imageUri,
            //   depthUri: depthImgUri,
            // });

            const {
              left,
              top,
              bottom,
              right
            } = getRawViewportDimension(fcanvasRef, _.get(curImageMetaData, 'width'), _.get(curImageMetaData, 'height'));
      
            if (prevViewportDimension.current &&
              prevViewportDimension.current.left === left &&
              prevViewportDimension.current.top === top &&
              prevViewportDimension.current.bottom === bottom &&
              prevViewportDimension.current.right === right
            ) {
              
              return;
            }
            
            prevViewportDimension.current = {
              left,
              top,
              bottom,
              right
            };

            await loadHighResolScene({
              fcanvasRef,
              rawImageW: _.get(curImageMetaData, 'width'),
              rawImageH: _.get(curImageMetaData, 'height'),
              displayedHighResolSceneRef,
              imageUri,
              depthUri: depthImgUri,
              type: 'image',
            });
          }

          const pointer = fcanvasRef.current.getPointer(opt.e);

          const zoom = fcanvasRef.current.getZoom();

          // update support line dimension
          delayUpdateSupportLine(
            fcanvasRef,
            _.get(curImageMetaData, 'width'),
            _.get(curImageMetaData, 'height'),
            pointer,
            zoom,
          );
        } else if (_.includes(['selectBottomLeftPixel', 'selectTopRightPixel'], mode.current)) {
          const pointer = fcanvasRef.current.getPointer(opt.e);
          const x = Math.floor(pointer.x);
          const y = Math.floor(pointer.y);

          if (x < 0 || x > curImageMetaData.width || y < 0 || y > curImageMetaData.height) {
            aoiAlert(t('notification.error.invalidPCBDimensionPoint'), ALERT_TYPES.COMMON_ERROR);
            return;
          }

          if (mode.current === 'selectBottomLeftPixel') {
            setCurBottemLeftPos({ x, y });
            // handlePickPointUpdateCameraPos('bl');
            updateCameraPos('bl', cameraPosRef.current);
          }
          if (mode.current === 'selectTopRightPixel') {
            setCurTopRightPos({ x, y });
            // handlePickPointUpdateCameraPos('tr');
            updateCameraPos('tr', cameraPosRef.current);
          }

          setSelectedTool('transform');
          mode.current = 'transform';
        }
      });

      fcanvasRef.current.on('mouse:wheel', (opt) => {
        if (isLocked.current) return;

        const delta = opt.e.deltaY;
        let zoom = fcanvasRef.current.getZoom();
        zoom *= 0.999 ** delta;
        // if (zoom > 20) zoom = 20;
        // if (zoom < 0.01) zoom = 0.01;
        fcanvasRef.current.zoomToPoint({ x: opt.e.offsetX, y: opt.e.offsetY }, zoom);
        opt.e.preventDefault();
        opt.e.stopPropagation();

        const {
          left,
          top,
          bottom,
          right
        } = getRawViewportDimension(fcanvasRef, _.get(curImageMetaData, 'width'), _.get(curImageMetaData, 'height'));
  
        if (prevViewportDimension.current &&
          prevViewportDimension.current.left === left &&
          prevViewportDimension.current.top === top &&
          prevViewportDimension.current.bottom === bottom &&
          prevViewportDimension.current.right === right) return;
        
        prevViewportDimension.current = {
          left,
          top,
          bottom,
          right
        };

        // reload high resol scene
        delayLoadHighSoluScene({
          fcanvasRef,
          rawImageW: _.get(curImageMetaData, 'width'),
          rawImageH: _.get(curImageMetaData, 'height'),
          displayedHighResolSceneRef,
          imageUri,
          depthUri: depthImgUri,
        });

        const pointer = fcanvasRef.current.getPointer(opt.e);

        delayUpdateSupportLine(
          fcanvasRef,
          _.get(curImageMetaData, 'width'),
          _.get(curImageMetaData, 'height'),
          pointer,
          zoom,
        );
      });

      if (_.isString(imageUri) && _.isString(depthImgUri)) {
        await loadInitFullSizeThumbnail({
          fcanvas: fcanvasRef.current,
          rawWidth: _.get(curImageMetaData, 'width'),
          rawHeight: _.get(curImageMetaData, 'height'),
          thumbnailBgSceneRef,
          imageUri,
          depthUri: depthImgUri,
          type: 'image',
        });
        resetView();
        await loadHighResolScene({
          fcanvasRef,
          rawImageW: _.get(curImageMetaData, 'width'),
          rawImageH: _.get(curImageMetaData, 'height'),
          displayedHighResolSceneRef: displayedHighResolSceneRef,
          imageUri,
          depthUri: depthImgUri,
          type: 'image',
          callback: () => {
            updateZIndex();
          },
        });

        // updateZIndex();
      }

      if (!resizeHandlerRef.current) {
        resizeHandlerRef.current = () => {
          if (!fcanvasRef.current || !viewerContainerRef.current) return;
          fcanvasRef.current.setWidth(viewerContainerRef.current.offsetWidth);
          fcanvasRef.current.setHeight(viewerContainerRef.current.offsetHeight);
        };

        containerResizeObserverRef.current = new ResizeObserver(resizeHandlerRef.current);
        containerResizeObserverRef.current.observe(viewerContainerRef.current);
      }
    };

    init(curImageMetaData, imageUri, thumbnailUri, depthImgUri);
  }, [curImageMetaData]);

  // useEffect(() => {
  //   if (!fcanvasRef.current || _.isEmpty(imageUri) || _.isEmpty(thumbnailUri) || _.isEmpty(depthImgUri)) return;

  //   const updateScene = async (imageUri, depthImgUri, thumbnailUri) => {
  //     await loadInitFullSizeThumbnail({
  //       fcanvas: fcanvasRef.current,
  //       rawWidth: _.get(curImageMetaData, 'width'),
  //       rawHeight: _.get(curImageMetaData, 'height'),
  //       thumbnailBgSceneRef,
  //       imageUri,
  //       depthUri: depthImgUri,
  //       type: 'image',
  //     });
  //     await loadHighResolScene({
  //       fcanvasRef,
  //       rawImageW: _.get(curImageMetaData, 'width'),
  //       rawImageH: _.get(curImageMetaData, 'height'),
  //       displayedHighResolSceneRef,
  //       imageUri,
  //       depthUri: depthImgUri,
  //       type: 'image',
  //       callback: () => {
  //         updateZIndex();
  //       },
  //     });
  //   };

  //   // updateScene(imageUri, depthImgUri, thumbnailUri);
  //   updateScene(imageUri, depthImgUri, thumbnailUri);
  // }, [
  //   imageUri,
  //   thumbnailUri,
  //   depthImgUri,
  // ]);

  useEffect(() => {
    cameraPosRef.current = cameraPosition;
  }, [cameraPosition]);

  useEffect(() => {
    return () => {
      if (fcanvasRef.current) {
        destroyFabricCanvas(fcanvasRef.current);
        fcanvasRef.current = null;
      }
    };
  }, []);

  return (
    <div className='relative w-full h-full '>
      { !_.isEmpty(curImageMetaData) && !_.isUndefined(curImageMetaData) &&
        <div
          className='absolute '
          style={{
            display: selectedTool === 'transform' ? 'none' : 'block',
            top: `${selectDimensionMousePos.curMouseTop}px`,
            left: `${selectDimensionMousePos.curMouseLeft + 10}px`,
            // border: `2px solid ${this.state.curBoxHeight >= defineProductBboxBoundaryLengthLimit && this.state.curBoxWidth >= defineProductBboxBoundaryLengthLimit ? '#81F499' : '#EB5757'}`,
            borderRadius: '4px',
            // background: `${this.state.curBoxHeight >= defineProductBboxBoundaryLengthLimit && this.state.curBoxWidth >= defineProductBboxBoundaryLengthLimit ? '#81F499' : '#EB5757'}`,
            background: '#56CCF2',
            zIndex: 11,
          }}
        >
          <div className='flex items-center gap-1 px-2'>
            <span
              className='font-source text-[12px] font-semibold'
              style={{ color: selectDimensionMousePos.curX < 0 || selectDimensionMousePos.curX > curImageMetaData.width ? '#EB5757' : '#131313' }}
            >
              {`X: ${selectDimensionMousePos.curX}`}
            </span>
            <span className='font-source text-[12px] font-semibold text-[#131313]'>,</span>
            <span
              className='font-source text-[12px] font-semibold'
              style={{ color: selectDimensionMousePos.curY < 0 || selectDimensionMousePos.curY > curImageMetaData.height ? '#EB5757' : '#131313' }}
            >
              {`Y: ${selectDimensionMousePos.curY}`}
            </span>
          </div>
        </div>
      }
      <canvas ref={canvasElRef} />
    </div>
  );
};

export default PCBDimensionViewer;