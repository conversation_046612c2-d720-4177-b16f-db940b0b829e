import React, { useCallback, useEffect, useRef, useState } from 'react';
import { fabric } from 'fabric';
import { generalPan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, generalPanZoom<PERSON>ouse<PERSON><PERSON><PERSON><PERSON><PERSON>, generalPanZoom<PERSON>ouse<PERSON>p<PERSON>andler, generalPanZoomMouseWheelHandler, getTwoDRectPminPmax, loadHighResolScene, loadInitFullSizeThumbnail, zoomPanToObject } from './util';
import { define3DViewBoxLimit, focusRectStrokeWidth, highResoluRefreshInterval } from '../common/const';
import _ from 'lodash';
import { useDispatch } from 'react-redux';
import { setContainerLvlLoadingMsg, setIsContainerLvlLoadingEnabled, setTransparentLoadingEnabled } from '../reducer/setting';
import { useGetImageMetaDataQuery } from '../services/camera';
import { useTranslation } from 'react-i18next';
import { destroyFabricCanvas } from '../common/util';


const FullPCBCaptureViewer = (props) => {
  const {
    selectedTool,
    setSelectedTool,
    handleSubmitSelect3DArea,
    imageUri,
    depthUri,
    manuallUpdateState,
    handleSubmitSelectAutoGenArea,
    autoProgramInspectionRegion,
  } = props;

  const dispatch = useDispatch();

  const { t } = useTranslation();

  const canvasElRef = useRef();
  const fcanvasRef = useRef();
  const isSceneLocked = useRef(false);
  const isPanning = useRef(false);
  const curRectInitMousePosRef = useRef(null);
  const curSelect3DAreaRectRef = useRef(null);
  const displayedHighResolSceneRef = useRef(null);
  const thumbnailBgSceneRef = useRef(null);
  const viewerInitialized = useRef(false);
  const viewerContainerRef = useRef(null);
  const autoGenInspectionRegionRef = useRef(null);

  const [curSelect3DBoxInfo, setCurSelect3DBoxInfo] = useState(null);

  const { data: curImageMetaData, refetch: refetchImageMetaData } = useGetImageMetaDataQuery({ uri: imageUri });

  const handleDrawBoxMouseDown = (opt) => {
    const pointer = fcanvasRef.current.getPointer(opt.e);
    const rect = new fabric.Rect({
      left: pointer.x,
      top: pointer.y,
      width: 0,
      height: 0,
      fill: 'transparent',
      stroke: 'red',
      strokeWidth: 10,
      selectable: false,
      strokeUniform: true, // Ensure stroke width remains consistent when scaling
      evented: false,
    });

    curRectInitMousePosRef.current = {
      x: pointer.x,
      y: pointer.y,
    };
    curSelect3DAreaRectRef.current = rect;

    fcanvasRef.current.add(curSelect3DAreaRectRef.current);

    const mousePos = fcanvasRef.current.getPointer(opt.e, true);

    setCurSelect3DBoxInfo({
      curBoxWidth: 0,
      curBoxHeight: 0,
      curMouseTop: mousePos.y,
      curMouseLeft: mousePos.x,
    });

    updateZIndex();
  };

  const handleDrawBoxMouseMove = (opt) => {
    if (!curSelect3DAreaRectRef.current || !curRectInitMousePosRef.current) return;

    const pointer = fcanvasRef.current.getPointer(opt.e);
    const mousePos = fcanvasRef.current.getPointer(opt.e, true);

    // NOTE: keep rect's width and height positive when drawing and adjust the top left
    // ow the rect's left and right will be hidden for some reason 2024/10/22
    if (_.get(curRectInitMousePosRef.current, 'x') > pointer.x) {
      curSelect3DAreaRectRef.current.set({
        left: pointer.x,
        width: curRectInitMousePosRef.current.x - pointer.x,
      })
    } else {
      curSelect3DAreaRectRef.current.set({
        width: pointer.x - curRectInitMousePosRef.current.x,
        left: curRectInitMousePosRef.current.x,
      });
    }

    if (_.get(curRectInitMousePosRef.current, 'y') > pointer.y) {
      curSelect3DAreaRectRef.current.set({
        top: pointer.y,
        height: curRectInitMousePosRef.current.y - pointer.y,
      });
    } else {
      curSelect3DAreaRectRef.current.set({
        height: pointer.y - curRectInitMousePosRef.current.y,
        top: curRectInitMousePosRef.current.y,
      });
    }

    curSelect3DAreaRectRef.current.moveTo(2);

    setCurSelect3DBoxInfo({
      curBoxWidth: curSelect3DAreaRectRef.current.width,
      curBoxHeight: curSelect3DAreaRectRef.current.height,
      curMouseTop: mousePos.y,
      curMouseLeft: mousePos.x,
    });

    updateZIndex();
  };

  const handleDrawBoxMouseUp = (imageUri, depthUri, mode) => {
    if (!curSelect3DAreaRectRef.current) return;

    curSelect3DAreaRectRef.current.setCoords();

    if (curSelect3DAreaRectRef.current.width < 0) {
      curSelect3DAreaRectRef.current.left += curSelect3DAreaRectRef.current.width;
      curSelect3DAreaRectRef.current *= -1;
    }
    if (curSelect3DAreaRectRef.current.height < 0) {
      curSelect3DAreaRectRef.current.top += curSelect3DAreaRectRef.current.height;
      curSelect3DAreaRectRef.current *= -1;
    }

    // get pmin pmax
    const { pMax, pMin } = getTwoDRectPminPmax(curSelect3DAreaRectRef.current, curSelect3DAreaRectRef.current.strokeWidth);

    if (mode === 'selectAutoGenArea') {
      // submit
      handleSubmitSelectAutoGenArea(pMin, pMax);
    } else if (mode === 'select3DArea') {
      // submit to get cropped 3d display
      handleSubmitSelect3DArea(imageUri, depthUri, pMin, pMax);
    }

    // clear the rect
    fcanvasRef.current.remove(curSelect3DAreaRectRef.current);
    curSelect3DAreaRectRef.current = null;
    setCurSelect3DBoxInfo(null);
    setSelectedTool('transform');

    updateZIndex();
  };

  const handleSwitchMode = (mode, imageUri, depthUri, curImageMetaData) => {
    if (!fcanvasRef.current) return;

    fcanvasRef.current.off('mouse:down');
    fcanvasRef.current.off('mouse:move');
    fcanvasRef.current.off('mouse:up');
    fcanvasRef.current.off('mouse:wheel');

    // 设置鼠标样式
    if (mode === 'selectAutoGenArea' || mode === 'select3DArea') {
      fcanvasRef.current.defaultCursor = 'crosshair';
      fcanvasRef.current.hoverCursor = 'crosshair';
      fcanvasRef.current.moveCursor = 'crosshair';
    } else {
      fcanvasRef.current.defaultCursor = 'default';
      fcanvasRef.current.hoverCursor = 'move';
      fcanvasRef.current.moveCursor = 'move';
    }

    switch (mode) {
      case 'transform':
        fcanvasRef.current.on('mouse:down', (opt) => generalPanZoomMouseDownHandler(opt, fcanvasRef, isPanning));
        fcanvasRef.current.on('mouse:move', (opt) => generalPanZoomMouseMoveHandler(opt, fcanvasRef, isPanning));
        fcanvasRef.current.on('mouse:up', () => {
          generalPanZoomMouseUpHandler(fcanvasRef, isPanning)
          // reload high resol scene
          if (_.isString(imageUri) && _.isString(depthUri)) delayLoadHighSoluScene({
            fcanvasRef,
            rawImageW: _.get(curImageMetaData, 'width'),
            rawImageH: _.get(curImageMetaData, 'height'),
            displayedHighResolSceneRef,
            imageUri,
            depthUri,
          });
        });
        fcanvasRef.current.on('mouse:wheel', (opt) => {
          generalPanZoomMouseWheelHandler(opt, fcanvasRef)
          // reload high resol scene
          if (_.isString(imageUri) && _.isString(depthUri)) delayLoadHighSoluScene({
            fcanvasRef,
            rawImageW: _.get(curImageMetaData, 'width'),
            rawImageH: _.get(curImageMetaData, 'height'),
            displayedHighResolSceneRef,
            imageUri,
            depthUri,
          });
        });
        break;
      case 'select3DArea':
        fcanvasRef.current.on('mouse:down', (opt) => handleDrawBoxMouseDown(opt));
        fcanvasRef.current.on('mouse:move', (opt) => handleDrawBoxMouseMove(opt));
        fcanvasRef.current.on('mouse:up', (opt) => handleDrawBoxMouseUp(imageUri, depthUri, 'select3DArea'));
        break;
      case 'selectAutoGenArea':
        fcanvasRef.current.on('mouse:down', (opt) => handleDrawBoxMouseDown(opt));
        fcanvasRef.current.on('mouse:move', (opt) => handleDrawBoxMouseMove(opt));
        fcanvasRef.current.on('mouse:up', (opt) => handleDrawBoxMouseUp(imageUri, depthUri, 'selectAutoGenArea'));
        break;
      default:
        break;
    }
  };

  const updateZIndex = () => {
    // bigger z-index called later
    if (thumbnailBgSceneRef.current) thumbnailBgSceneRef.current.moveTo(1);
    if (displayedHighResolSceneRef.current) displayedHighResolSceneRef.current.moveTo(2);
    if (curSelect3DAreaRectRef.current) curSelect3DAreaRectRef.current.moveTo(3);
    if (autoGenInspectionRegionRef.current) autoGenInspectionRegionRef.current.moveTo(4);
    fcanvasRef.current.renderAll();
  };

  const resetView = () => {
    if (!thumbnailBgSceneRef.current || !fcanvasRef.current) return;

    zoomPanToObject(thumbnailBgSceneRef.current, fcanvasRef.current, 0.01);
  };

  const delayLoadHighSoluScene = useCallback(
    _.debounce(async ({
      fcanvasRef,
      rawImageW,
      rawImageH,
      displayedHighResolSceneRef,
      imageUri,
      depthUri,
    }) => {
      dispatch(setTransparentLoadingEnabled(true));

      await loadHighResolScene({
        fcanvasRef,
        rawImageW,
        rawImageH,
        displayedHighResolSceneRef,
        imageUri,
        depthUri,
        type: 'image',
        callback: () => {
          updateZIndex();
        },
      });

      dispatch(setTransparentLoadingEnabled(false));
    }, highResoluRefreshInterval),
  [curImageMetaData]);

  useEffect(() => {
    if (!fcanvasRef.current || !autoProgramInspectionRegion || _.isEmpty(autoProgramInspectionRegion)) {
      if (autoGenInspectionRegionRef.current) {
        fcanvasRef.current.remove(autoGenInspectionRegionRef.current);
        autoGenInspectionRegionRef.current = null;
      }
      return;
    }

    if (autoGenInspectionRegionRef.current) {
      fcanvasRef.current.remove(autoGenInspectionRegionRef.current);
    }

    const rect = new fabric.Rect({
      left: autoProgramInspectionRegion.pmin.x - focusRectStrokeWidth,
      top: autoProgramInspectionRegion.pmin.y - focusRectStrokeWidth,
      width: autoProgramInspectionRegion.pmax.x - autoProgramInspectionRegion.pmin.x + focusRectStrokeWidth,
      height: autoProgramInspectionRegion.pmax.y - autoProgramInspectionRegion.pmin.y + focusRectStrokeWidth,
      fill: 'transparent',
      stroke: 'red',
      strokeWidth: focusRectStrokeWidth,
      selectable: false,
      strokeUniform: true,
      evented: false,
    });

    autoGenInspectionRegionRef.current = rect;
    fcanvasRef.current.add(autoGenInspectionRegionRef.current);
    updateZIndex();

  }, [autoProgramInspectionRegion]);

  useEffect(() => {
    if (!fcanvasRef.current) return;

    handleSwitchMode(selectedTool, imageUri, depthUri, curImageMetaData);
  }, [selectedTool]);

  useEffect(() => {
    if (manuallUpdateState === 0) return;
    refetchImageMetaData();
  }, [manuallUpdateState]);

  useEffect(() => {
    if (_.isEmpty(curImageMetaData) || !viewerContainerRef.current) return;

    if (!fcanvasRef.current) {
      fcanvasRef.current = new fabric.Canvas(canvasElRef.current, {
        antialias: 'off',
        uniformScaling: false,
      });
    } else {
      // clear all objects
      fcanvasRef.current.clear();
    }

    const fcanvas = fcanvasRef.current;

    fcanvas.setWidth(viewerContainerRef.current.offsetWidth);
    fcanvas.setHeight(viewerContainerRef.current.offsetHeight);

    handleSwitchMode(selectedTool, imageUri, depthUri, curImageMetaData);

    const initLoadScene = async (imageUri, depthUri, curImageMetaData) => {
      if (_.isString(imageUri) && _.isString(depthUri)) {
        await loadInitFullSizeThumbnail({
          fcanvas: fcanvasRef.current,
          rawWidth: _.get(curImageMetaData, 'width'),
          rawHeight: _.get(curImageMetaData, 'height'),
          thumbnailBgSceneRef,
          imageUri,
          depthUri,
          type: 'image',
        });
        await loadHighResolScene({
          fcanvasRef,
          rawImageW: _.get(curImageMetaData, 'width'),
          rawImageH: _.get(curImageMetaData, 'height'),
          displayedHighResolSceneRef,
          imageUri,
          depthUri,
          type: 'image',
        });
        updateZIndex();
        resetView();
      }
    };

    initLoadScene(imageUri, depthUri, curImageMetaData);
  }, [curImageMetaData, manuallUpdateState]);

  useEffect(() => {
    return () => {
      if (fcanvasRef.current) {
        destroyFabricCanvas(fcanvasRef.current);
        fcanvasRef.current = null;
      }
    };
  }, []);

  return (
    <div className='relative w-full h-full'>
      { !_.isEmpty(curSelect3DBoxInfo) &&
        <div
          className='absolute z-[20]'
          style={{
            display: _.isEmpty(curSelect3DBoxInfo) ? 'none' : 'block',
            top: `${_.get(curSelect3DBoxInfo, 'curMouseTop', 0)}px`,
            left: `${_.get(curSelect3DBoxInfo, 'curMouseLeft', 0) + 10}px`,
            borderRadius: '4px',
            background: '#56CCF2',
          }}
        >
          <div className='flex items-center gap-1 px-2'>
            <span
              className='font-source text-[12px] font-semibold'
              // style={{ color: selectedTool === 'select3DArea' && _.get(curSelect3DBoxInfo, 'curBoxWidth', 0) <= define3DViewBoxLimit.width ? '#131313' : '#EB5757' }}
              style={{ color: '#131313' }}
            >
              {curSelect3DBoxInfo.curBoxWidth.toFixed(0)}
            </span>
            <span
              className='font-source text-[12px] font-semibold text-[#131313]'
            >x</span>
            <span
              className='font-source text-[12px] font-semibold'
              // style={{ color: selectedTool === 'select3DArea' && _.get(curSelect3DBoxInfo, 'curBoxHeight', 0) <= define3DViewBoxLimit.height ? '#131313' : '#EB5757' }}
              style={{ color: '#131313' }}
            >
              {curSelect3DBoxInfo.curBoxHeight.toFixed(0)}
            </span>
          </div>
        </div>
      }
      <div
        className='absolute top-0 left-0 w-full h-full z-[10]'
        ref={viewerContainerRef}
      >
        <canvas ref={canvasElRef} />
      </div>
    </div>
  );
};

export default FullPCBCaptureViewer;