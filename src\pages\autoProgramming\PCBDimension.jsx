import { Button, InputNumber, Switch } from 'antd';
import React, { Fragment, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import _ from 'lodash';
import { useLazyGetCameraCaptureFrameUriQuery, useMoveCameraMutation } from '../../services/camera';
import { useDispatch, useSelector } from 'react-redux';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import { setContainerLvlLoadingMsg, setIsContainerLvlLoadingEnabled } from '../../reducer/setting';
import { DarkBlueDefaultButtonConfigProvider } from '../../common/styledComponent';
import PCBDimensionDisplay from './PCBDimensionDisplay';
import { useMeasureProductDimensionMutation } from '../../services/product';


const PCBDimension = (props) => {
  const {
    panelHeight,
    panelWidth,
    setPanelHeight,
    setPanelWidth,
    curProduct,
    refetchCurProduct,
  } = props;

  const dispatch = useDispatch();

  const { t } = useTranslation();

  const [displayedPanelHeight, setDisplayedPanelHeight] = useState(panelHeight);
  const [displayedPanelWidth, setDisplayedPanelWidth] = useState(panelWidth);
  const [isMeasureWithCameraEnabled, setIsMeasureWithCameraEnabled] = useState(false);

  const [cameraPosition, setCameraPosition] = useState({
    x: 0,
    y: 0,
    z: 0,
  });
  const [current2DUri, setCurrent2DUri] = useState('');
  const [currentDepthImgUri, setCurrentDepthImgUri] = useState('');
  const [currentThumbnailUri, setCurrentThumbnailUri] = useState('');
  const [curBottemLeftPos, setCurBottemLeftPos] = useState(null);
  const [curTopRightPos, setCurTopRightPos] = useState(null);
  const [selectedTool, setSelectedTool] = useState('transform'); // transform, selectBottomLeftPixel, selectTopRightPixel
  const [cameraBLPos, setCameraBLPos] = useState(null);
  const [cameraTRPos, setCameraTRPos] = useState(null);
  const [currentDimension, setCurrentDimension] = useState(null);

  const [measureProductDimension] = useMeasureProductDimensionMutation();
  const [moveCamera] = useMoveCameraMutation();
  const [getCameraCaptureFrameUri] = useLazyGetCameraCaptureFrameUriQuery();

  const cameraAccessToken = useSelector((state) => state.setting.cameraAccessToken);

  const handleMoveCameraSubmit = async (x, y, z, cameraAccessToken) => {
    if (!_.isNumber(x) || !_.isNumber(y) || !_.isNumber(z) || _.isEmpty(cameraAccessToken)) {
      return;
    }

    const res = await moveCamera({
      x,
      y,
      z,
      camera_access_token: cameraAccessToken,
    });

    if (res.error) {
      aoiAlert(t('notification.error.moveCamera'), ALERT_TYPES.COMMON_ERROR);
      console.error('moveCamera error:', _.get(res, 'error.message', ''));
      return;
    }

    aoiAlert(t('notification.success.moveCamera'), ALERT_TYPES.COMMON_INFO);
    return;
  };

  const handleTwoDCapture = async (cameraAccessToken, curProduct) => {
    if (_.isEmpty(cameraAccessToken)) return;

    dispatch(setIsContainerLvlLoadingEnabled(true));
    dispatch(setContainerLvlLoadingMsg(t('loader.cameraCapturing'))); 
  
    const res = await getCameraCaptureFrameUri({
      // camera_access_token: cameraAccessToken,
      camera_id: 0,
      // product_id: Number(_.get(curProduct, 'product_id', 0)),
    });

    dispatch(setIsContainerLvlLoadingEnabled(false));
    dispatch(setContainerLvlLoadingMsg(''));

    if (res.error) {
      aoiAlert(t('notification.error.getCameraCaptureFrameUri'), ALERT_TYPES.COMMON_ERROR);
      console.error('getCameraCaptureFrameUri error:', _.get(res, 'error.message', ''));
      return;
    }

    setCurrent2DUri(_.get(res, 'data.image.data_uri', ''));
    setCurrentDepthImgUri(_.get(res, 'data.depth_image.data_uri', ''));
    setCurrentThumbnailUri(_.get(res, 'data.thumbnail_image.data_uri', ''));
  };

  const handlePickPointUpdateCameraPos = (type, cameraPosition) => {
    switch (type) {
      case 'bl':
        setCameraBLPos(cameraPosition);
        break;
      case 'tr':
        setCameraTRPos(cameraPosition);
        break;
      default:
        break;
    }
  };

  const handleCalculateDimension = async (curBottemLeftPos, curTopRightPos, cameraBLPos, cameraTRPos) => {
    if (_.isEmpty(curBottemLeftPos) || _.isEmpty(curTopRightPos) || _.isEmpty(cameraBLPos) || _.isEmpty(cameraTRPos)) {
      aoiAlert(t('notification.error.selectPCBDimensionPixel'), ALERT_TYPES.COMMON_ERROR);
      return;
    }
    // check if the selected bottom left is on the left of the top right
    // if (curBottemLeftPos.x >= curTopRightPos.x || curBottemLeftPos.y <= curTopRightPos.y) {
    //   aoiAlert(t('notification.error.invalidPCBDimensionPoint'), ALERT_TYPES.COMMON_ERROR);
    //   return;
    // }

    // calc dimension
    const res = await measureProductDimension({
      "bl_cam_pos": {
        "x": cameraBLPos.x,
        "y": cameraBLPos.y,
        "z": cameraBLPos.z
      },
      "bl_corner_pos": {
        "x": curBottemLeftPos.x,
        "y": curBottemLeftPos.y
      },
      "tr_cam_pos": {
        "x": cameraTRPos.x,
        "y": cameraTRPos.y,
        "z": cameraTRPos.z
      },
      "tr_corner_pos": {
        "x": curTopRightPos.x,
        "y": curTopRightPos.y
      }
    });

    if (res.error) {
      aoiAlert(t('notification.error.measureProductDimension'), ALERT_TYPES.COMMON_ERROR);
      console.error('measureProductDimension error:', _.get(res, 'error.message', ''));
      return;
    }

    // setCurrentDimension({
    //   width: _.get(res, 'data.board_width_mm', 0),
    //   height: _.get(res, 'data.board_height_mm', 0),
    // });
    setDisplayedPanelHeight(_.get(res, 'data.board_height_mm', 0));
    setDisplayedPanelWidth(_.get(res, 'data.board_width_mm', 0));
    setPanelHeight(_.get(res, 'data.board_height_mm', 0));
    setPanelWidth(_.get(res, 'data.board_width_mm', 0));
  };

  useEffect(() => {
    setDisplayedPanelHeight(panelHeight);
    setDisplayedPanelWidth(panelWidth);
  }, [
    panelHeight,
    panelWidth,
  ]);

  return (
    <div className='flex flex-1 gap-0.5 self-stretch'>
      <div className='flex flex-1 self-stretch bg-[#000]'>
        <PCBDimensionDisplay
          current2DUri={current2DUri}
          currentDepthImgUri={currentDepthImgUri}
          currentThumbnailUri={currentThumbnailUri}
          selectedTool={selectedTool}
          setSelectedTool={setSelectedTool}
          curProduct={curProduct}
          refetchCurProduct={refetchCurProduct}
          setCurBottemLeftPos={setCurBottemLeftPos}
          setCurTopRightPos={setCurTopRightPos}
          cameraPosition={cameraPosition}
          handlePickPointUpdateCameraPos={handlePickPointUpdateCameraPos}
        />
      </div>
      <div className='flex w-[293px] flex-col gap-2 self-stretch bg-[#ffffff05]'>
        <div className='flex p-4 gap-1 self-stretch flex-col'>
          <div className='flex flex-col gap-1 self-stretch'>
            <span className='font-source text-[16px] font-normal leading-[150%]'>
              {t('productDefine.PCBDimension')}
            </span>
            <span className='font-source text-[12px] font-normal leading-[150%]'>
              {t('autoProgramming.enterTheDimensionIfKnown')}
            </span>
          </div>
          <div className='flex flex-col gap-2 flex-1 self-stretch'>
            <div className='flex gap-2 items-center self-stretch'>
              
              <span className='w-[142px] font-source text-[14px] font-normal leading-[150%]'>
                {t('autoProgramming.panelWidth')}
              </span>
              <div className='flex w-6 h-6 justify-center items-center'>
                <img
                  src='/icn/panelWidth_color.svg'
                  className='w-3 h-4'
                  alt='panelWidth'
                />
              </div>
              <InputNumber
                min={1}
                style={{ width: '50%' }}
                controls={false}
                value={displayedPanelHeight}
                onChange={(value) => {
                  setDisplayedPanelHeight(value);
                  setPanelHeight(value);
                }}
              />
            </div>
            <div className='flex gap-2 items-center self-stretch'>
              <span className='w-[142px] font-source text-[14px] font-normal leading-[150%]'>
                {t('autoProgramming.panelLength')}
              </span>
              <div className='flex w-6 h-6 justify-center items-center'>
                <img
                  src='/icn/panelLength_color.svg'
                  className='w-3 h-4'
                  alt='panelLength_color'
                />
              </div>
              <InputNumber
                min={1}
                style={{ width: '50%' }}
                controls={false}
                value={displayedPanelWidth}
                onChange={(value) => {
                  setDisplayedPanelWidth(value);
                  setPanelWidth(value);
                }}
              />
            </div>
          </div>
        </div>
        <div className='w-full h-[1px] bg-[#4F4F4F]' />
        <div className='flex p-4 gap-6 flex-col self-stretch'>
          <div className='flex flex-col gap-1 self-stretch'>
            <div className='flex items-center gap-2 self-stretch'>
              <Switch
                size='small'
                checked={isMeasureWithCameraEnabled}
                onChange={(checked) => setIsMeasureWithCameraEnabled(checked)}
              />
              <span className='font-source text-[16px] font-normal leading-[150%]'>
                {t('autoProgramming.measureWithCamera')}
              </span>
            </div>
            <span className='font-source text-[12px] font-normal leading-[150%]'>
              {t('autoProgramming.moveTheCameraToThree')}
            </span>
          </div>
          {isMeasureWithCameraEnabled &&
          <Fragment>
          <div className='flex gap-2 flex-col self-stretch'>
            <span className='font-source text-[14px] font-normal leading-[150%]'>
              {t('autoProgramming.inputCameraPosition')}
            </span>
            <div className='grid grid-cols-3 gap-4 self-stretch'>
              <div className='flex items-center gap-1 self-stretch'>
                <span className='font-source text-[12px] font-normal leading-[150%]'>
                  X:
                </span>
                <InputNumber
                  style={{ width: '100%', height: '26px' }}
                  controls={false}
                  value={cameraPosition.x}
                  onChange={(value) => setCameraPosition((prev) => ({ ...prev, x: value }))}
                />
              </div>
              <div className='flex items-center gap-1 self-stretch'>
                <span className='font-source text-[12px] font-normal leading-[150%]'>
                  Y:
                </span>
                <InputNumber
                  style={{ width: '100%', height: '26px' }}
                  controls={false}
                  value={cameraPosition.y}
                  onChange={(value) => setCameraPosition((prev) => ({ ...prev, y: value }))}
                />
              </div>
              <div className='flex items-center gap-1 self-stretch'>
                <span className='font-source text-[12px] font-normal leading-[150%]'>
                  Z:
                </span>
                <InputNumber
                  style={{ width: '100%', height: '26px' }}
                  controls={false}
                  value={cameraPosition.z}
                  onChange={(value) => setCameraPosition((prev) => ({ ...prev, z: value }))}
                />
              </div>
            </div>
            <Button
              onClick={() => handleMoveCameraSubmit(cameraPosition.x, cameraPosition.y, cameraPosition.z, cameraAccessToken)}
            >
              <span className='font-source text-[12px] font-normal leading-[normal]'>
                {t('productDefine.moveCamera')}
              </span>
            </Button>
            <Button
              onClick={() => handleTwoDCapture(cameraAccessToken, curProduct)}
            >
              <span className='font-source text-[12px] font-normal leading-[normal]'>
                {t('autoProgramming.capture2D')}
              </span>
            </Button>
          </div>
          <div className='flex flex-col gap-2 self-stretch'>
            <div className='flex py-1 items-center self-stretch'>
              <span className='font-source text-[14px] font-normal leading-[150%]'>
                {t('autoProgramming.PCBDimensions')}
              </span>
            </div>
            <div className='flex flex-col self-stretch'>
              <div className='flex py-1 flex-col gap-2 self-stretch'>
                <span className='font-source text-[14px] font-normal leading-[150%]'>
                  {t('productDefine.bottomLeftPixel')}:
                </span>
                <div className='flex gap-2 self-stretch'>
                  <div className='py-1 px-2 h-[26px] flex items-center justify-center flex-1 bg-[#ffffff0d] rounded-[4px] self-stretch'>
                    <span className='font-source text-[12px] font-normal leading-[normal] whitespace-nowrap'>
                      {_.isEmpty(curBottemLeftPos) ? t('common.undefined') : `${curBottemLeftPos.x}, ${curBottemLeftPos.y}`}
                    </span>
                  </div>
                  <div className='flex items-center flex-1 self-stretch h-[26px]'>
                    {_.isEmpty(curBottemLeftPos) ?
                      <DarkBlueDefaultButtonConfigProvider>
                        <Button
                          style={{ width: '100%' }}
                          onClick={() => {
                            if (_.isEmpty(current2DUri)) {
                              aoiAlert(t('notification.error.pleaseCapture2DImageFirst'), ALERT_TYPES.COMMON_ERROR);
                              return;
                            }
                            setSelectedTool('selectBottomLeftPixel');
                          }}
                        >
                          <div className='flex items-center gap-1 justify-center self-stretch px-2'>
                            <img src='/icn/locator_blue.svg' alt='locator' className='w-3 h-3' />
                            <span className='font-source text-[12px] font-normal text-AOI-blue pt-0.5'>
                              {t('productDefine.selectPixel')}
                            </span>
                          </div>
                        </Button>
                      </DarkBlueDefaultButtonConfigProvider>
                    :
                      <DarkBlueDefaultButtonConfigProvider>
                        <Button
                          style={{ width: '100%' }}
                          onClick={() => {
                            setCurBottemLeftPos(null);
                          }}
                        >
                          <div className='flex items-center gap-1 justify-center self-stretch px-2'>
                            <span className='font-source text-[12px] font-normal text-AOI-blue'>
                              {t('common.reset')}
                            </span>
                          </div>
                        </Button>
                      </DarkBlueDefaultButtonConfigProvider>
                    }
                  </div>
                </div>
              </div>
              <div className='flex py-1 flex-col gap-2 self-stretch'>
                <span className='font-source text-[14px] font-normal leading-[150%]'>
                  {t('productDefine.topRightPixel')}:
                </span>
                <div className='flex gap-2 self-stretch'>
                  <div className='py-1 px-2 h-[26px] flex items-center justify-center flex-1 bg-[#ffffff0d] rounded-[4px] self-stretch'>
                    <span className='font-source text-[12px] font-normal leading-[normal] whitespace-nowrap'>
                      {_.isEmpty(curTopRightPos) ? t('common.undefined') : `${curTopRightPos.x + displayedPanelWidth}, ${curTopRightPos.y}`}
                    </span>
                  </div>
                  <div className='flex items-center flex-1 self-stretch h-[26px]'>
                    {_.isEmpty(curTopRightPos) ?
                      <DarkBlueDefaultButtonConfigProvider>
                        <Button
                          style={{ width: '100%' }}
                          onClick={() => {
                            if (_.isEmpty(current2DUri)) {
                              aoiAlert(t('notification.error.pleaseCapture2DImageFirst'), ALERT_TYPES.COMMON_ERROR);
                              return;
                            }
                            setSelectedTool('selectTopRightPixel');
                          }}
                        >
                          <div className='flex items-center gap-1 justify-center self-stretch px-2'>
                            <img src='/icn/locator_blue.svg' alt='locator' className='w-3 h-3' />
                            <span className='font-source text-[12px] font-normal text-AOI-blue pt-0.5'>
                              {t('productDefine.selectPixel')}
                            </span>
                          </div>
                        </Button>
                      </DarkBlueDefaultButtonConfigProvider>
                      :
                      <DarkBlueDefaultButtonConfigProvider>
                        <Button
                          style={{ width: '100%' }}
                          onClick={() => {
                            setCurTopRightPos(null);
                          }}
                        >
                          <div className='flex items-center gap-1 justify-center self-stretch px-2'>
                            <span className='font-source text-[12px] font-normal text-AOI-blue'>
                              {t('common.reset')}
                            </span>
                          </div>
                        </Button>
                      </DarkBlueDefaultButtonConfigProvider>
                    }
                  </div>
                </div>
              </div>
            </div>
            <Button
              onClick={() => {
                handleCalculateDimension(curBottemLeftPos, curTopRightPos, cameraBLPos, cameraTRPos);
              }}
              disabled={_.isEmpty(curBottemLeftPos) || _.isEmpty(curTopRightPos)}
            >
              <span className='font-source text-[12px] font-normal leading-[normal]'>
                {t('productDefine.calcualateDimension')}
              </span>
            </Button>
          </div>
          </Fragment>
          }
        </div>
      </div>
    </div>
  );
};

export default PCBDimension;