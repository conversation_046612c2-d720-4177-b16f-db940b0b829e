import React, { useCallback, useEffect, useRef } from 'react';
import { fabric } from 'fabric';
import {
	flip2DPointBasedOnPoint,
	generalPanZoomMouseDownHandler,
	generalPanZoomMouse<PERSON>oveHandler,
	generalPanZoom<PERSON>ouse<PERSON>p<PERSON>andler,
	generalPanZoomMouseWheelHandler,
	getRotatedPointPos,
	loadHighResolScene,
	loadInitFullSizeThumbnail,
	middlePanZoomMouseDownHandler,
  zoomPanToObject,
} from './util';
import _ from 'lodash';
import { useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import {
	setContainerLvlLoadingMsg,
	setIsContainerLvlLoadingEnabled,
	setTransparentLoadingEnabled,
} from '../reducer/setting';
import {
	defaultCircleRadius,
	highResoluRefreshInterval,
	newRectStrokeWidth,
} from '../common/const';
import { use } from 'react';
import { destroyFabricCanvas } from '../common/util';

const AlignCoordViewer = (props) => {
	const {
		curProduct,
		prodMetadata,
		parsedComponentInfo,
		currentTranslation,
		setCurrentTranslation,
		currentRotation,
		setCurrentRotation,
		editAlignCoord,
		horizontallyFlipped,
		verticallyFlipped,
	} = props;

	const dispatch = useDispatch();
	const { t } = useTranslation();

	const canvasElRef = useRef(null);
	const containerRef = useRef(null);
	const fcanvasRef = useRef(null);
	const thumbnailBgSceneRef = useRef(null);
	const displayedHighResolSceneRef = useRef(null);
	const isPanning = useRef(false);
	const displayedComponents = useRef([]);
	const curComponentsActiveSelection = useRef(null);
	const prevComponentsActiveSelectionCenter = useRef(null);
	const initComponentsCenter = useRef(null);
	const isHorizontallyFlipped = useRef(false);
	const isVerticallyFlipped = useRef(false);

  const resetView = () => {
    if (!thumbnailBgSceneRef.current || !fcanvasRef.current) return;
    zoomPanToObject(thumbnailBgSceneRef.current, fcanvasRef.current, 0.01);
  };

	const loadComponentInfo = (
		components,
		currentTranslation,
		currentRotation,
		isHorizontallyFlipped,
		isVerticallyFlipped
	) => {
		if (!fcanvasRef.current) return;

		if (!_.isEmpty(displayedComponents.current)) {
			for (const c of displayedComponents.current) {
				fcanvasRef.current.remove(c);
			}
			displayedComponents.current = [];
		}

		// generate circles for each component
		// use components' x, y as center, also apply currentTranslation
		const zoom = fcanvasRef.current.getZoom();
		// const circleRadius = Math.min(defaultCircleRadius / zoom, defaultCircleRadius);
		const circleRadius = defaultCircleRadius;
		let minX = Infinity;
		let minY = Infinity;
		let maxX = -Infinity;
		let maxY = -Infinity;

		for (const c of components) {
			const circle = new fabric.Circle({
				radius: circleRadius,
				fill: 'red',
				stroke: 'red',
				strokeWidth: circleRadius,
				left: c.x - circleRadius + currentTranslation.x,
				top: c.y - circleRadius + currentTranslation.y,
				selectable: false,
				evented: false,
			});

			minX = Math.min(minX, c.x);
			minY = Math.min(minY, c.y);
			maxX = Math.max(maxX, c.x);
			maxY = Math.max(maxY, c.y);

			circle.set('originalX', c.x);
			circle.set('originalY', c.y);
			fcanvasRef.current.add(circle);
			displayedComponents.current.push(circle);
		}

		initComponentsCenter.current = {
			x: (maxX - minX) / 2 + minX,
			y: (maxY - minY) / 2 + minY,
		};

		handleAllComponentRotation(
			currentRotation,
			currentTranslation,
			isHorizontallyFlipped,
			isVerticallyFlipped
		);

		handleAllComponentFlip(
			'horizontal',
		);

		updateZIndex();
	};

	const initComponentsActiveSelection = () => {
		if (!fcanvasRef.current) return;
		if (_.isEmpty(displayedComponents.current)) return;

		fcanvasRef.current.discardActiveObject();

		curComponentsActiveSelection.current = new fabric.ActiveSelection(
			displayedComponents.current,
			{
				canvas: fcanvasRef.current,
			}
		);
		curComponentsActiveSelection.current.set(
			'currentTranslation',
			currentTranslation
		);
		curComponentsActiveSelection.current.set('initAngle', currentRotation);
		curComponentsActiveSelection.current.setControlsVisibility({
			mt: false,
			mb: false,
			ml: false,
			mr: false,
			tl: false,
			tr: false,
			bl: false,
			br: false,
			mtr: false,
		});

		prevComponentsActiveSelectionCenter.current =
			curComponentsActiveSelection.current.getCenterPoint();

		curComponentsActiveSelection.current.on('modified', () => {
			if (!curComponentsActiveSelection.current) return;

			// compare with prev center and update currentTranslation
			const curCenter = curComponentsActiveSelection.current.getCenterPoint();
			const diffX = _.round(
				curCenter.x - prevComponentsActiveSelectionCenter.current.x,
				0
			);
			const diffY = _.round(
				curCenter.y - prevComponentsActiveSelectionCenter.current.y,
				0
			);
			// console.log('diffX', diffX, 'diffY', diffY);
			setCurrentTranslation({
				x: curComponentsActiveSelection.current.currentTranslation.x + diffX,
				y: curComponentsActiveSelection.current.currentTranslation.y + diffY,
			});
			curComponentsActiveSelection.current.set('currentTranslation', {
				x: curComponentsActiveSelection.current.currentTranslation.x + diffX,
				y: curComponentsActiveSelection.current.currentTranslation.y + diffY,
			});
			setCurrentRotation(
				(curComponentsActiveSelection.current.angle +
					curComponentsActiveSelection.current.initAngle) %
					360
			);
			prevComponentsActiveSelectionCenter.current = curCenter;
		});

		fcanvasRef.current.setActiveObject(curComponentsActiveSelection.current);
		fcanvasRef.current.requestRenderAll();
	};

	const handleAllComponentRotation = (
		angle,
		currentTranslation,
		isHorizontallyFlipped,
		isVerticallyFlipped
	) => {
		if (_.isEmpty(displayedComponents.current) || !initComponentsCenter.current)
			return;

		for (const c of displayedComponents.current) {
			let flippedCenter = getRotatedPointPos(
				initComponentsCenter.current,
				{ x: c.get('originalX'), y: c.get('originalY') },
				angle
			);

			const circleRadius = defaultCircleRadius;

			// also apply flip
			if (isHorizontallyFlipped) {
				flippedCenter = flip2DPointBasedOnPoint(
					flippedCenter,
					initComponentsCenter.current,
					'horizontal'
				);
			}

			// if (isVerticallyFlipped) {
			//   flippedCenter = flip2DPointBasedOnPoint(
			//     flippedCenter,
			//     initComponentsCenter.current,
			//     'vertical',
			//   );
			// }

			const roundedPos = {
				x: _.round(flippedCenter.x, 0),
				y: _.round(flippedCenter.y, 0),
			};

			c.set({
				left: roundedPos.x + currentTranslation.x - circleRadius,
				top: roundedPos.y + currentTranslation.y - circleRadius,
			});
		}

		updateZIndex();
	};

	const handleDeselectComponents = () => {
		if (!fcanvasRef.current) return;

		curComponentsActiveSelection.current = null;
		fcanvasRef.current.discardActiveObject().renderAll();

		return;
	};

	const handleAllComponentFlip = (flipType) => {
		fcanvasRef.current.discardActiveObject().renderAll();

		// get current center of all component center points
		const currentCenter = {
			x: initComponentsCenter.current.x + currentTranslation.x,
			y: initComponentsCenter.current.y + currentTranslation.y,
		};

		for (const c of displayedComponents.current) {
			const { x, y } = flip2DPointBasedOnPoint(
				c.getCenterPoint(),
				currentCenter,
				flipType
			);

			const circleRadius = defaultCircleRadius;
			c.set({
				left: x - circleRadius,
				top: y - circleRadius,
			});
		}

		if (flipType === 'horizontal') {
			isHorizontallyFlipped.current = horizontallyFlipped;
		}
		// else if (flipType === 'vertical') {
		//   isVerticallyFlipped.current = verticallyFlipped;
		// }

		updateZIndex();
	};

	const delayLoadHighSoluScene = useCallback(
		_.debounce(
			async ({
				fcanvasRef,
				rawImageW,
				rawImageH,
				displayedHighResolSceneRef,
				imageUri,
				depthUri,
			}) => {
				dispatch(setTransparentLoadingEnabled(true));

				await loadHighResolScene({
					fcanvasRef,
					rawImageW,
					rawImageH,
					displayedHighResolSceneRef,
					imageUri,
					depthUri,
					type: 'image',
					callback: () => {
						updateZIndex();
					},
				});

				dispatch(setTransparentLoadingEnabled(false));
			},
			highResoluRefreshInterval
		),
		[prodMetadata]
	);

	const updateZIndex = () => {
		if (thumbnailBgSceneRef.current) thumbnailBgSceneRef.current.moveTo(1);
		if (displayedHighResolSceneRef.current)
			displayedHighResolSceneRef.current.moveTo(2);
		if (!_.isEmpty(displayedComponents.current)) {
			for (const c of displayedComponents.current) {
				c.moveTo(3);
			}
		}

		fcanvasRef.current.renderAll();
	};

	const handleSwitchMode = (mode, imageUri, depthUri, curImageMetaData) => {
		if (!fcanvasRef.current) return;

		fcanvasRef.current.off('mouse:down');
		fcanvasRef.current.off('mouse:move');
		fcanvasRef.current.off('mouse:up');
		fcanvasRef.current.off('mouse:wheel');

		switch (mode) {
			case 'transform':
				fcanvasRef.current.on('mouse:down', (opt) => {
					if (opt.e.button === 0) {
						generalPanZoomMouseDownHandler(opt, fcanvasRef, isPanning);
					} else if (opt.e.button === 1) {
						middlePanZoomMouseDownHandler(fcanvasRef, isPanning);
					} else if (opt.e.button === 2) {
						handleDeselectComponents();
					}
				});
				fcanvasRef.current.on('mouse:move', (opt) =>
					generalPanZoomMouseMoveHandler(opt, fcanvasRef, isPanning)
				);
				fcanvasRef.current.on('mouse:up', () => {
					generalPanZoomMouseUpHandler(fcanvasRef, isPanning);
					if (_.isString(imageUri) && _.isString(depthUri))
						delayLoadHighSoluScene({
							fcanvasRef,
							rawImageW: _.get(curImageMetaData, 'width'),
							rawImageH: _.get(curImageMetaData, 'height'),
							displayedHighResolSceneRef,
							imageUri,
							depthUri,
						});
				});
				fcanvasRef.current.on('mouse:wheel', (opt) => {
					generalPanZoomMouseWheelHandler(opt, fcanvasRef);
					if (_.isString(imageUri) && _.isString(depthUri))
						delayLoadHighSoluScene({
							fcanvasRef,
							rawImageW: _.get(curImageMetaData, 'width'),
							rawImageH: _.get(curImageMetaData, 'height'),
							displayedHighResolSceneRef,
							imageUri,
							depthUri,
						});
				});
				break;
			case 'adjustCoord':
				break;
			default:
				break;
		}
	};

	useEffect(() => {
		if (!fcanvasRef.current) return;

		fcanvasRef.current.discardActiveObject();
		curComponentsActiveSelection.current = null;

		handleAllComponentRotation(
			currentRotation,
			currentTranslation,
			isHorizontallyFlipped.current,
			isVerticallyFlipped.current
		);
	}, [currentRotation]);

	useEffect(() => {
		if (editAlignCoord === 0) {
			handleDeselectComponents();
		}
	}, [editAlignCoord]);

	useEffect(() => {
		if (editAlignCoord === 0) return;

		initComponentsActiveSelection();
	}, [editAlignCoord]);

	useEffect(() => {
		if (!fcanvasRef.current) return;
		if (horizontallyFlipped === isHorizontallyFlipped.current) return;
		if (_.isEmpty(displayedComponents.current)) return;

		handleAllComponentFlip('horizontal');
	}, [horizontallyFlipped]);

	useEffect(() => {
		if (!fcanvasRef.current) return;
		if (verticallyFlipped === isVerticallyFlipped.current) return;
		if (_.isEmpty(displayedComponents.current)) return;

		handleAllComponentFlip('vertical');
	}, [verticallyFlipped]);

	useEffect(() => {
		if (
			!canvasElRef.current ||
			!containerRef.current ||
			_.isUndefined(curProduct) ||
			_.isUndefined(prodMetadata)
		)
			return;

		if (!fcanvasRef.current) {
			fcanvasRef.current = new fabric.Canvas(canvasElRef.current, {
				antialias: 'off',
				uniformScaling: false,
				selection: false,
				fireMiddleClick: true,
				fireRightClick: true,
				stopContextMenu: true,
			});
			fcanvasRef.current.setHeight(containerRef.current.offsetHeight);
			fcanvasRef.current.setWidth(containerRef.current.offsetWidth);
		} else {
			fcanvasRef.current.clear();
		}

		const initLoadScene = async (
			imageUri,
			depthUri,
			curImageMetaData,
			parsedComponentInfo,
			currentTranslation
		) => {
			if (_.isString(imageUri) && _.isString(depthUri)) {
				await loadInitFullSizeThumbnail({
					fcanvas: fcanvasRef.current,
					rawWidth: _.get(curImageMetaData, 'width'),
					rawHeight: _.get(curImageMetaData, 'height'),
					thumbnailBgSceneRef,
					imageUri,
					depthUri,
					type: 'image',
				});
				await loadHighResolScene({
					fcanvasRef,
					rawImageW: _.get(curImageMetaData, 'width'),
					rawImageH: _.get(curImageMetaData, 'height'),
					displayedHighResolSceneRef,
					imageUri,
					depthUri,
					type: 'image',
				});

				handleSwitchMode('transform', imageUri, depthUri, curImageMetaData);
			}

			if (!_.isEmpty(parsedComponentInfo))
				loadComponentInfo(
					parsedComponentInfo,
					currentTranslation,
					currentRotation,
					isHorizontallyFlipped.current,
					isVerticallyFlipped.current
				);

			updateZIndex();
      resetView();
		};

		initLoadScene(
			_.get(curProduct, 'inspectables[0].color_map_uri'),
			_.get(curProduct, 'inspectables[0].depth_map_uri'),
			prodMetadata,
			parsedComponentInfo,
			currentTranslation
		);
	}, [curProduct, prodMetadata]);

	useEffect(() => {
		return () => {
			if (fcanvasRef.current) {
				destroyFabricCanvas(fcanvasRef.current);
				fcanvasRef.current = null;
			}
		};
	}, []);

	return (
		<div className="relative w-full h-full">
			<div
				className="absolute top-0 left-0 w-full h-full z-[20]"
				ref={containerRef}
			>
				<canvas ref={canvasElRef} />
			</div>
		</div>
	);
};

export default AlignCoordViewer;
