import { Button, ConfigProvider } from 'antd';
import React, { Fragment, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import TwoDCapture from '../teach/recipe/fullCaputre/TwoDCapture';
import ThreeDCapture from '../teach/recipe/fullCaputre/ThreeDCapture';
import _ from 'lodash';
import { useProductInspectablesRegisterMutation, useUpdateProductMutation } from '../../services/product';
import { useDispatch, useSelector } from 'react-redux';
import { setContainerLvlLoadingMsg, setIsContainerLvlLoadingEnabled } from '../../reducer/setting';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import FullCaptureDisplay from '../teach/recipe/fullCaputre/Display';
import { useLazyGetCameraCaptureFrameUriQuery } from '../../services/camera';
import { GreenDefaultButtonConfigProvider, GreenPrimaryButtonConfigProvider } from '../../common/styledComponent';
import { isAOI2DSMT, isAOI3DSMT } from '../../common/const';


const FullCapture = (props) => {
  const {
    curProduct,
    refetchCurProduct,
    setAutoProgramInspectionRegion,
    autoProgramInspectionRegion,
  } = props;

  const dispatch = useDispatch();

  const { t } = useTranslation();

  const settingContainer = useRef(null);

  const [selectedDimensionConfig, setSelectedDimensionConfig] = useState('2d');
  const [d2CaptureSettings, setD2CaptureSettings] = useState({});
  const [frame, setFrame] = useState({});
  const [algorithmParams, setAlgorithmParams] = useState({});
  const [settingHeight, setSettingHeight] = useState(0);
  const [settingWidth, setSettingWidth] = useState(0);
  const [manuallUpdateState, setManualUpdateState] = useState(0);
  const [curImageUri, setCurImageUri] = useState('');
  const [curDepthImgUri, setCurDepthImgUri] = useState('');
  const [fullCaptureDisplayed, setFullCaptureDisplayed] = useState(false);
  const [selectedTool, setSelectedTool] = useState('transform');

  const [updateProduct] = useUpdateProductMutation();
  const [productInspectablesRegister] = useProductInspectablesRegisterMutation();
  const [getCameraCaptureFrameUri] = useLazyGetCameraCaptureFrameUriQuery();

  const conveyorAccessToken = useSelector((state) => state.setting.conveyorAccessToken);
  const cameraAccessToken = useSelector((state) => state.setting.cameraAccessToken);

  const handleQuickCapture = async (
    cameraAccessToken,
    curProduct,
    d2CaptureSettings,
    frame,
    algorithmParams,
  ) => {
    if (_.isEmpty(cameraAccessToken)) return;

    dispatch(setIsContainerLvlLoadingEnabled(true));
    dispatch(setContainerLvlLoadingMsg(t('loader.cameraCapturing')));

    // save capture settings
    let updatedProduct = _.cloneDeep(curProduct);
    updatedProduct = _.set(updatedProduct, 'product_specific_sensor_config.sensors[0].camera_configs[0].config_2d', d2CaptureSettings);
    updatedProduct = _.set(updatedProduct, 'product_specific_sensor_config.sensors[0].camera_configs[0].config_3d.frame_settings', [frame]);
    updatedProduct = _.set(updatedProduct, 'product_specific_sensor_config.sensors[0].camera_configs[0].config_3d.algorithm_params', algorithmParams);

    const res1 = await updateProduct({
      product_id: Number(_.get(curProduct, 'product_id', 0)),
      product_specific_sensor_config: updatedProduct.product_specific_sensor_config,
    });

    if (res1.error) {
      console.error('updateProduct error', res1.error.message);
      aoiAlert(t('notification.error.updateProduct'), ALERT_TYPES.COMMON_ERROR);
      dispatch(setIsContainerLvlLoadingEnabled(false));
      dispatch(setContainerLvlLoadingMsg(''));
      setFullCaptureDisplayed(false);
      return;
    }

    aoiAlert(t('notification.success.updateProduct'), ALERT_TYPES.COMMON_INFO);

    dispatch(setIsContainerLvlLoadingEnabled(true));
    dispatch(setContainerLvlLoadingMsg(t('loader.cameraCapturing')));

    const res = await getCameraCaptureFrameUri({
      // camera_access_token: cameraAccessToken,
      camera_id: 0,
      product_id: Number(_.get(curProduct, 'product_id', 0)),
    });

    dispatch(setIsContainerLvlLoadingEnabled(false));
    dispatch(setContainerLvlLoadingMsg(''));

    if (res.error) {
      aoiAlert(t('notification.error.getCameraCaptureFrameUri'), ALERT_TYPES.COMMON_ERROR);
      console.error('getCameraCaptureFrameUri error:', _.get(res, 'error.message', ''));
      return;
    }

    setCurImageUri(_.get(res, 'data.image.data_uri', ''));
    setCurDepthImgUri(_.get(res, 'data.depth_image.data_uri', ''));
    setFullCaptureDisplayed(false);
  };

  const handleFullCapture = async ({
    d2CaptureSettings,
    frame,
    algorithmParams,
    curProduct,
    conveyorAccessToken,
    manuallUpdateState,
  }) => {
    if (!_.isString(conveyorAccessToken) || _.isEmpty(conveyorAccessToken)) return;

    dispatch(setIsContainerLvlLoadingEnabled(true));
    dispatch(setContainerLvlLoadingMsg(t('loader.productRegister')));

    // save capture settings
    let updatedProduct = _.cloneDeep(curProduct);
    updatedProduct = _.set(updatedProduct, 'product_specific_sensor_config.sensors[0].camera_configs[0].config_2d', d2CaptureSettings);
    updatedProduct = _.set(updatedProduct, 'product_specific_sensor_config.sensors[0].camera_configs[0].config_3d.frame_settings', [frame]);
    updatedProduct = _.set(updatedProduct, 'product_specific_sensor_config.sensors[0].camera_configs[0].config_3d.algorithm_params', algorithmParams);

    const res = await updateProduct({
      product_id: Number(_.get(curProduct, 'product_id', 0)),
      product_specific_sensor_config: updatedProduct.product_specific_sensor_config,
    });

    if (res.error) {
      console.error('updateProduct error', res.error.message);
      aoiAlert(t('notification.error.updateProduct'), ALERT_TYPES.COMMON_ERROR);
      dispatch(setIsContainerLvlLoadingEnabled(false));
      dispatch(setContainerLvlLoadingMsg(''));
      setFullCaptureDisplayed(false);
      return;
    }

    // instead of calling capture we do inspectables register
    const registerRes = await productInspectablesRegister({
      product_id: Number(_.get(curProduct, 'product_id', 0)),
      conveyor_access_token: conveyorAccessToken,
    });

    if (registerRes.error) {
      aoiAlert(t('notification.error.productInspectablesRegister'), ALERT_TYPES.COMMON_ERROR);
      console.error('productInspectablesRegister error:', registerRes.error.message);
      dispatch(setIsContainerLvlLoadingEnabled(false));
      dispatch(setContainerLvlLoadingMsg(''));
      setFullCaptureDisplayed(false);
      return;
    }

    dispatch(setIsContainerLvlLoadingEnabled(false));
    dispatch(setContainerLvlLoadingMsg(''));

    const newProdRes = await refetchCurProduct();

    // console.log('newProdRes', newProdRes);
    // console.log('compare color map', _.get(newProdRes, 'data.inspectables[0].color_map_uri') === _.get(curProduct, 'inspectables[0].color_map_uri'));

    // NOTE: if registere product the color/depth map uri will NOT be different
    setCurImageUri(_.get(newProdRes, 'data.inspectables[0].color_map_uri'));
    setCurDepthImgUri(_.get(newProdRes, 'data.inspectables[0].depth_map_uri'));
    setManualUpdateState(manuallUpdateState + 1);
    setFullCaptureDisplayed(true);
  };

  useEffect(() => {
    // init capture related settings
    // we only have one camera and one step for now
    setFrame(_.get(curProduct, 'product_specific_sensor_config.sensors[0].camera_configs[0].config_3d.frame_settings[0]', {}));
    setD2CaptureSettings(_.get(curProduct, 'product_specific_sensor_config.sensors[0].camera_configs[0].config_2d', {}));
    setAlgorithmParams(_.get(curProduct, 'product_specific_sensor_config.sensors[0].camera_configs[0].config_3d.algorithm_params', {}));
  }, [curProduct]);

  useEffect(() => {
    if (!settingContainer.current) return;
    setSettingHeight(settingContainer.current.clientHeight);
    setSettingWidth(settingContainer.current.clientWidth);
  }, []);

  return (
    <div className='flex flex-1 self-stretch gap-0.5'>
      <div className='flex flex-1 self-stretch'>
        <FullCaptureDisplay
          curImageUri={curImageUri}
          curDepthImgUri={curDepthImgUri}
          manuallUpdateState={manuallUpdateState}
          fullCaptureDisplayed={fullCaptureDisplayed}
          isInAutoGenSetting={true}
          selectedTool={selectedTool}
          setSelectedTool={setSelectedTool}
          autoProgramInspectionRegion={autoProgramInspectionRegion}
          setAutoProgramInspectionRegion={setAutoProgramInspectionRegion}
        />
      </div>
      <div className='flex w-[293px] flex-col gap-4 self-stretch bg-[#ffffff05]'>
        <div className='flex p-4 self-stretch gap-8 flex-col'>
          <div className='flex flex-col gap-2 self-stretch'>
            <div className='flex py-2 gap-1 self-stretch flex-col'>
              <span className='font-source text-[16px] font-normal leading-[150%]'>
                {isAOI2DSMT ? t('autoProgramming.registerProduct') : t('autoProgramming.full2DAnd3D')}
              </span>
              <span className='font-source text-[12px] font-normal leading-[150%] text-gray-5'>
                {isAOI2DSMT ? t('autoProgramming.performAFullCapture2d') : t('autoProgramming.performAFullCapture')}
              </span>
            </div>
            <div className='flex gap-2 self-stretch items-center'>
              {_.isEmpty(_.get(curProduct, 'inspectables', [])) ?
                <GreenPrimaryButtonConfigProvider>
                  <Button
                    style={{ width: '100%' }}
                    type='primary'
                    onClick={() => {
                      handleFullCapture({
                        d2CaptureSettings,
                        frame,
                        algorithmParams,
                        curProduct,
                        conveyorAccessToken,
                        manuallUpdateState,
                      });
                    }}
                  >
                    <div className='flex gap-2 items-center'>
                      <img src='/icn/play_black.svg' alt='play' className='w-[8.5px] h-[10px]' />
                      <span className='font-source text-[12px] font-semibold leading-[150%] pt-0.5'>
                        {isAOI2DSMT ? t('productDefine.capture2D') : t('productDefine.capture2dAnd3d')}
                      </span>
                    </div>
                  </Button>
                </GreenPrimaryButtonConfigProvider>
              :
                <GreenDefaultButtonConfigProvider>
                  <Button
                    style={{ width: '100%' }}
                    type='default'
                    onClick={() => {
                      handleFullCapture({
                        d2CaptureSettings,
                        frame,
                        algorithmParams,
                        curProduct,
                        conveyorAccessToken,
                        manuallUpdateState,
                      });
                    }}
                  >
                    <div className='flex gap-2 items-center'>
                      <img src='/icn/play_white.svg' alt='play' className='w-[8.5px] h-[10px]' />
                      <span className='font-source text-[12px] font-semibold leading-[150%] pt-0.5'>
                        {isAOI2DSMT ? t('productDefine.capture2D') : t('productDefine.capture2dAnd3d')}
                      </span>
                    </div>
                  </Button>
                </GreenDefaultButtonConfigProvider>
              }
            </div>
          </div>
          <div className='flex gap-2 self-stretch flex-col'>
            <div className='flex flex-col items-start gap-1 self-stretch px-0 py-2'>
              <span className='text-sm font-normal leading-[150%] tracking-[0.42px] font-source'>
                {t('autoProgramming.setInspectionRegion')}
              </span>
              <span className='text-xs font-normal leading-[150%] font-source text-gray-5'>
                {t('autoProgramming.defineWhereToAuto')}
              </span>
            </div>
            <Button
              disabled={!fullCaptureDisplayed}
              onClick={() => {
                setAutoProgramInspectionRegion(null);
                setSelectedTool('selectAutoGenArea');
              }}
            >
              <span className='font-source text-[12px] font-semibold leading-[150%]'>
                {t('autoProgramming.defineRegion')}
              </span>
            </Button>
          </div>
        </div>
        <div className='flex flex-col items-start gap-0.5 flex-[1_0_0] self-stretch'>
          <div className='flex flex-col items-start gap-1 self-stretch px-4 py-2'>
            <span className='text-sm font-normal leading-[150%] tracking-[0.42px] font-source'>
              {t('autoProgramming.lightSettings')}
            </span>
          </div>
          {isAOI3DSMT && (
          <div className='flex justify-center items-center gap-1 self-stretch border-t-gray-2 border-b-gray-2 px-0 py-px border-t border-solid border-b'>
            <ConfigProvider
              theme={{
                components: {
                  Button: {
                    primaryColor: '#fff',
                    colorPrimary: '#ffffff0d',
                    colorPrimaryHover: '#ffffff0d',
                    colorPrimaryActive: '#ffffff0d',
                  }
                }
              }}
            >
              <Button
                style={{ width: '50%' }}
                type={selectedDimensionConfig === '2d' ? 'primary' : 'text'}
                onClick={() => setSelectedDimensionConfig('2d')}
              >
                <span className={`font-source text-[12px] ${selectedDimensionConfig === '2d' ? 'font-semibold' : 'font-normal'} leading-[150%]`}>
                  {t('productDefine.twoDCapture')}
                </span>
              </Button>
              <Button
                style={{ width: '50%' }}
                type={selectedDimensionConfig === '3d' ? 'primary' : 'text'}
                onClick={() => setSelectedDimensionConfig('3d')}
              >
                <span className={`font-source text-[12px] ${selectedDimensionConfig === '3d' ? 'font-semibold' : 'font-normal'} leading-[150%]`}>
                  {t('productDefine.threeDCapture')}
                </span>
              </Button>
            </ConfigProvider>
          </div>
          )}
          <div className='flex flex-col items-start flex-[1_0_0] self-stretch [background:rgba(255,255,255,0.05)] px-0 py-2'>
            <div className='flex flex-col items-start gap-1 self-stretch py-0 flex-1'>
              <div
                className='flex flex-1 self-stretch'
                ref={settingContainer}
              >
                <div
                  className='flex flex-col items-start gap-1 self-stretch overflow-y-auto'
                  style={{
                    height: `${settingHeight}px`,
                    width: `${settingWidth}px`
                  }}
                >
                  {selectedDimensionConfig === '2d' && (
                    <TwoDCapture
                      d2CaptureSettings={d2CaptureSettings}
                      setD2CaptureSettings={setD2CaptureSettings}
                    />
                  )}
                  {selectedDimensionConfig === '3d' && isAOI3DSMT && (
                    <ThreeDCapture
                      frame={frame}
                      setFrame={setFrame}
                      algorithmParams={algorithmParams}
                      setAlgorithmParams={setAlgorithmParams}
                    />
                  )}
                </div>
              </div>
              <div className='flex flex-col items-start gap-6 self-stretch px-3 py-3'>
                <Button
                  style={{ width: '100%' }}
                  onClick={() => handleQuickCapture(cameraAccessToken, curProduct, d2CaptureSettings, frame, algorithmParams)}
                >
                  <span className='font-source text-[12px] font-normal leading-[150%]'>
                    {t('productDefine.quick2DCapture')}
                  </span>
                </Button>
                <Button
                  style={{ width: '100%' }}
                >
                  <span className='font-source text-[12px] font-normal leading-[150%]'>
                    {t('productDefine.resetToDefault')}
                  </span>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FullCapture;