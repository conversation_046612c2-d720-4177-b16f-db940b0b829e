import React, { useEffect, useState } from 'react';
import { CustomModal } from '../common/styledComponent';
import { useTranslation } from 'react-i18next';
import { Button, Input, Select } from 'antd';
import { localStorageKeys } from '../common/const';


const UpdateHost = (props) => {
  const {
    isOpened,
    setIsOpened,
  } = props;

  const { t } = useTranslation();

  const [uriPrefix, setUriPrefix] = useState('http://');
	const [newHost, setNewHost] = useState('');

	useEffect(() => {
    if (!isOpened) return;

		const serverEndpoint =
			localStorage.getItem('serverHost') || 'http://localhost:8000';
		setUriPrefix(serverEndpoint.split('://')[0] + '://');
		setNewHost(serverEndpoint.split('://')[1]);
	}, [isOpened]);

  return (
    <CustomModal
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      title={<span className='font-source text-[16px] font-semibold leading-[150%]'>
        {t('updateBackendHost.title')}
      </span>}
      footer={null}
    >
      <div className='flex p-4 items-center self-stretch flex-col gap-4'>
        <div className='flex flex-col gap-2 self-stretch'>
          <span className='font-source text-[14px] text-gray-3 font-normal leading-[normal] tracking-[0.6px]'>
            {t('updateBackendHost.titleDesc1')}
          </span>
          <span className="font-source text-[14px] text-gray-3 font-normal leading-[normal] tracking-[0.6px]">
            {t('updateBackendHost.titleDesc2')}
          </span>
        </div>
        <div className="flex flex-col items-start gap-8 self-stretch">
					<div className="flex items-center gap-2 self-stretch">
						<span className="font-source text-[14px] font-normal whitespace-nowrap">
							{t('updateBackendHost.currentHost')}
						</span>
						<div className="flex gap-1 items-center w-full">
							<Select
								size="small"
								options={[
									{
										label: 'http://',
										value: 'http://',
									},
									// {
									// 	label: 'https://',
									// 	value: 'https://',
									// },
								]}
								value={uriPrefix}
								popupMatchSelectWidth={false}
								onChange={(value) => setUriPrefix(value)}
								style={{ width: '120px' }}
							/>
							<Input
								size="small"
								value={newHost}
								onChange={(e) => setNewHost(e.target.value)}
								style={{ width: '100%' }}
							/>
						</div>
					</div>
        </div>
        <Button
          style={{ width: '100%' }}
          onClick={() => {
            localStorage.setItem('serverHost', `${uriPrefix}${newHost}`);
            localStorage.setItem(localStorageKeys.accessToken, '');
            localStorage.setItem(localStorageKeys.userRole, '');
            window.location.reload();
            setIsOpened(false);
          }}
        >
          <span
            className='font-source text-[12px] font-normal'
          >
            {t('common.save')}
          </span>
        </Button>
      </div>
    </CustomModal>
  );
};

export default UpdateHost;