import React, { useState, useEffect, useRef, Fragment } from 'react';
import _ from 'lodash';
import { serverHost, newRectStrokeWidth } from '../common/const';

const LightweightImageViewer = ({
  components,
  features,
  isInspectedView,
  goldenProduct,
  selectedDCid,
  selectedFid,
  selectedArrayIndex
}) => {
  const containerRef = useRef();
  const imageRef = useRef();
  const [imageScale, setImageScale] = useState({ scaleX: 1, scaleY: 1, offsetX: 0, offsetY: 0 });
  const [containerDimensions, setContainerDimensions] = useState({ width: 0, height: 0 });

  // Get image URL
  const getImageUrl = () => {
    if (isInspectedView) {
      const component = _.find(components, c => c.result_component_id === selectedDCid && c.array_index === selectedArrayIndex);
      if (!component) return '';

      let url = `${serverHost}/blob?type=image`;
      url += `&color_uri=${encodeURIComponent(_.get(component, 'color_map_uri', ''))}`;
      url += `&depth_uri=${encodeURIComponent(_.get(component, 'depth_map_uri', ''))}`;
      return url;
    } else {
      let url = `${serverHost}/blob?type=image`;
      url += `&color_uri=${encodeURIComponent(_.get(goldenProduct, 'inspectables[0].color_map_uri', ''))}`;
      url += `&depth_uri=${encodeURIComponent(_.get(goldenProduct, 'inspectables[0].depth_map_uri', ''))}`;
      return url;
    }
  };

  // Get selected component for highlighting
  const getSelectedComponent = () => {
    if (isInspectedView) return null; // Don't show component rect in inspected view
    return _.find(components, c => c.region_group_id === selectedDCid && c.array_index === selectedArrayIndex);
  };

  // Get selected feature for highlighting
  const getSelectedFeature = () => {
    if (isInspectedView) {
      return _.find(features, f => f.component_id === selectedDCid && f.feature_id === selectedFid);
    } else {
      return _.find(features, f => f.group_id === selectedDCid && f.feature_id === selectedFid && f.array_index === selectedArrayIndex);
    }
  };

  // Convert image coordinates to screen coordinates
  const imageToScreenCoords = (x, y) => {
    return {
      x: x * imageScale.scaleX + imageScale.offsetX,
      y: y * imageScale.scaleY + imageScale.offsetY
    };
  };

  const imageUrl = getImageUrl();
  const selectedComponent = getSelectedComponent();
  const selectedFeature = getSelectedFeature();

  // Calculate container dimensions and image scaling
  useEffect(() => {
    const updateDimensions = async (
      selectedComponent,
      selectedFeature
    ) => {
      if (!containerRef.current) return;

      const container = containerRef.current;
      const containerWidth = container.offsetWidth;
      const containerHeight = container.offsetHeight;

      let scale = 1;
      let offsetX = 0;
      let offsetY = 0;
      let imageWidth = 0;
      let imageHeight = 0;
      let rawImageWidth = 0;
      let rawImageHeight = 0;

      // calc the scaled image dimension and top left
      if (isInspectedView) {
        // the scene's dimension would be component's dimension
        rawImageWidth = _.get(selectedComponent, 'shape.points[1].x', 0) - _.get(selectedComponent, 'shape.points[0].x', 0) + 1;
        rawImageHeight = _.get(selectedComponent, 'shape.points[1].y', 0) - _.get(selectedComponent, 'shape.points[0].y', 0) + 1;
      } else {
        // fetch image metadata
        let metadataRes;
        try {
          metadataRes = await fetch(`${serverHost}/image/metadata?uri=${encodeURIComponent(_.get(goldenProduct, 'inspectables[0].color_map_uri', ''))}`);
        } catch (error) {
          console.error('Failed to get image metadata');
          return;
        }

        const metadata = await metadataRes.json();
        rawImageWidth = _.get(metadata, 'data.width', 0);
        rawImageHeight = _.get(metadata, 'data.height', 0);
      }

      // check if we scale based on height or width
      if (containerWidth / containerHeight > rawImageWidth / rawImageHeight) {
        // container is wider than the component, so we scale based on height
        scale = containerHeight / rawImageHeight;
        imageWidth = rawImageWidth * scale;
        imageHeight = containerHeight;
        offsetX = (containerWidth - imageWidth) / 2;
        offsetY = 0;
      } else {
        // container is taller than the component, so we scale based on width
        scale = containerWidth / rawImageWidth;
        imageWidth = containerWidth;
        imageHeight = rawImageHeight * scale;
        offsetX = 0;
        offsetY = (containerHeight - imageHeight) / 2;
      }

      setContainerDimensions({
        width: containerWidth,
        height: containerHeight
      });

      // Set a simple 1:1 scale for now - you can adjust this based on your needs
      setImageScale({
        scaleX: scale,
        scaleY: scale,
        offsetX,
        offsetY
      });
    };

    console.log('selectedComponent', selectedComponent);
    console.log('selectedFeature', selectedFeature);

    updateDimensions(
      selectedComponent,
      selectedFeature
    );
    window.addEventListener('resize', updateDimensions);
    return () => window.removeEventListener('resize', updateDimensions);
  }, [
    imageUrl,
    selectedComponent,
    selectedFeature
  ]);

  if (!imageUrl) return <div className="w-full h-full bg-black" />;

  return (
    <div ref={containerRef} className="relative w-full h-full bg-black overflow-hidden">
      {containerDimensions.width > 0 && containerDimensions.height > 0 && (
        <Fragment>
          {/* scene image */}
          <img
            ref={imageRef}
            src={imageUrl}
            alt={isInspectedView ? "Inspected view" : "Golden view"}
            style={{
              width: `${containerDimensions.width}px`,
              height: `${containerDimensions.height}px`,
              objectFit: 'contain',
              zIndex: 1
            }}
          />

          {/* Component ROI overlay (only for golden view) */}
          {selectedComponent && !isInspectedView && imageScale.scaleX > 0 && (
            <div
              className="absolute border-2"
              style={{
                zIndex: 2,
                left: `${_.get(selectedComponent, 'shape.points[0].x', 0) * imageScale.scaleX + imageScale.offsetX}px`,
                top: `${_.get(selectedComponent, 'shape.points[0].y', 0) * imageScale.scaleY + imageScale.offsetY}px`,
                width: `${(_.get(selectedComponent, 'shape.points[1].x', 0) - _.get(selectedComponent, 'shape.points[0].x', 0) + 1) * imageScale.scaleX}px`,
                height: `${(_.get(selectedComponent, 'shape.points[1].y', 0) - _.get(selectedComponent, 'shape.points[0].y', 0) + 1) * imageScale.scaleY}px`,
                borderColor: '#56CCF2',
                backgroundColor: 'transparent',
                pointerEvents: 'none'
              }}
            />
          )}

          {/* Feature ROI overlay */}
          {selectedFeature && imageScale.scaleX > 0 && (() => {
            // Calculate feature center and dimensions like the original implementation
            const featureCenter = {
              x: _.get(selectedFeature, 'roi.points[0].x', 0) + (_.get(selectedFeature, 'roi.points[1].x', 0) - _.get(selectedFeature, 'roi.points[0].x', 0) + 1) / 2,
              y: _.get(selectedFeature, 'roi.points[0].y', 0) + (_.get(selectedFeature, 'roi.points[1].y', 0) - _.get(selectedFeature, 'roi.points[0].y', 0) + 1) / 2,
            };

            const featureWidth = _.get(selectedFeature, 'roi.points[1].x', 0) - _.get(selectedFeature, 'roi.points[0].x', 0) + 1;
            const featureHeight = _.get(selectedFeature, 'roi.points[1].y', 0) - _.get(selectedFeature, 'roi.points[0].y', 0) + 1;

            // Position the rectangle by centering it on the feature center (matching original implementation)
            const rectLeft = featureCenter.x - featureWidth / 2 - newRectStrokeWidth;
            const rectTop = featureCenter.y - featureHeight / 2 - newRectStrokeWidth;

            // Convert to screen coordinates
            const screenCoords = imageToScreenCoords(rectLeft, rectTop);
            const screenCenter = imageToScreenCoords(featureCenter.x, featureCenter.y);

            return (
              <>
                <div
                  className="absolute border-2"
                  style={{
                    zIndex: 2,
                    left: `${screenCoords.x}px`,
                    top: `${screenCoords.y}px`,
                    width: `${(featureWidth + newRectStrokeWidth) * imageScale.scaleX}px`,
                    height: `${(featureHeight + newRectStrokeWidth) * imageScale.scaleY}px`,
                    borderColor: '#56CCF2',
                    backgroundColor: 'transparent',
                    transform: `rotate(${_.get(selectedFeature, 'roi.angle', 0)}deg)`,
                    transformOrigin: 'center',
                    pointerEvents: 'none'
                  }}
                />

                <div
                  className="absolute"
                  style={{
                    zIndex: 3,
                    left: `${screenCenter.x - 20}px`,
                    top: `${screenCoords.y - 40}px`,
                    width: '40px',
                    height: '40px',
                    color: '#EB5757',
                    pointerEvents: 'none'
                  }}
                >
                  <svg width="40" height="40" viewBox="0 0 40 40" fill="currentColor">
                    <path d="M20 35 L15 25 L25 25 Z" />
                  </svg>
                </div>
              </>
            );
          })()}
        </Fragment>
      )}
    </div>
  );
};

export default LightweightImageViewer;
