import React from 'react';
import { CustomModal } from '../common/styledComponent';
import { useDispatch, useSelector } from 'react-redux';
import { setContainerLvlLoadingMsg, setCurTrainingTaskStartTime, setGlobalRetrainInfo, setIsContainerLvlLoadingEnabled, setIsGlobalRetrainReminderOpened, setIsTrainingRunning } from '../reducer/setting';
import { useTranslation } from 'react-i18next';
import { Button } from 'antd';
import { useLazyGetAllSessionsQuery, useLazyGetInferenceStatusQuery, useModelUpdateTriggerMutation } from '../services/inference';
import { modelTypes } from '../common/const';
import { ALERT_TYPES, aoiAlert } from '../common/alert';
import { applyTemplateString, getRunningTaskGoldenProductIds, sleep } from '../common/util';
import _ from 'lodash';


const GlobalRetrainReminder = () => {
  const dispatch = useDispatch();

  const { t } = useTranslation();

  const [retrainTrigger] = useModelUpdateTriggerMutation();
  // const [getAllSession] = useLazyGetAllSessionsQuery();
  const [lazyGetConveyorInferenceStatus] = useLazyGetInferenceStatusQuery();

  const isGlobalRetrainReminderOpened = useSelector(state => state.setting.isGlobalRetrainReminderOpened);
  const globalRetrainInfo = useSelector(state => state.setting.globalRetrainInfo);

  const handleRetrainTrigger = async (productId) => {
    dispatch(setIsContainerLvlLoadingEnabled(true));
    dispatch(setContainerLvlLoadingMsg(t('loader.modelTraining')));

    const runGoldenIds = await getRunningTaskGoldenProductIds(lazyGetConveyorInferenceStatus);

    if (_.includes(runGoldenIds, Number(productId))) {
      dispatch(setIsContainerLvlLoadingEnabled(false));
      dispatch(setContainerLvlLoadingMsg(''));
      aoiAlert(t('notification.error.pleaseStopAllInspectionTaskRelatedToThisProduct'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    const res = await retrainTrigger({
      model_types: [
        modelTypes.mountingModel,
        modelTypes.leadModel,
        // modelTypes.textVerificationModel,
        // modelTypes.textDirectionModel,
      ],
      golden_product_id: productId,
      update_parameters: false,
    });

    await sleep(3 * 1000); // ensure task schuedule is written in db

    if (res.error) {
      dispatch(setIsContainerLvlLoadingEnabled(false));
      dispatch(setContainerLvlLoadingMsg(''));
      console.error('retrainTrigger error:', _.get(res, 'error.message', ''));
      aoiAlert(t('notification.error.retrainModel'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    dispatch(setIsTrainingRunning(true));
    dispatch(setCurTrainingTaskStartTime(new Date().getTime()));
    dispatch(setIsGlobalRetrainReminderOpened(false));
    dispatch(setGlobalRetrainInfo({}));
  };

  return (
    <CustomModal
      open={isGlobalRetrainReminderOpened}
      onCancel={() => {
        dispatch(setIsGlobalRetrainReminderOpened(false));
        dispatch(setGlobalRetrainInfo({}));
      }}
      footer={null}
      title={
        <span className='font-source text-[16px] font-semibold leading-[normal]'>
          {t('globalRetrainReminder.title')}
        </span>
      }
    >
      <div className='flex py-6 px-4 flex-col gap-8 self-stretch'>
        <span className='font-source text-[12px] font-normal leading-[150%]'>
          {applyTemplateString(t('globalRetrainReminder.youHaveUpdatedProductSoMightWantToRetrainModel'), { productName: globalRetrainInfo.productName })}
        </span>
        <div className='flex gap-2 items-center self-stretch'>
          <Button
            style={{ width: '50%' }}
            onClick={() => {
              dispatch(setIsGlobalRetrainReminderOpened(false));
              dispatch(setGlobalRetrainInfo({}));
            }}
          >
            <span className='font-source text-[12px] font-normal leading-[150%]'>
              {t('common.ignore')}
            </span>
          </Button>
          <Button
            type='primary'
            style={{ width: '50%' }}
            onClick={() => {
              handleRetrainTrigger(globalRetrainInfo.productId);
            }}
          >
            <span className='font-source text-[12px] font-normal leading-[150%]'>
              {t('common.retrainNow')}
            </span>
          </Button>
        </div>
      </div>
    </CustomModal>
  );
};

export default GlobalRetrainReminder;