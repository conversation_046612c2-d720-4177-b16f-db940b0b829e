import React, { useEffect, useMemo, useState } from 'react';
import { Button, DatePicker, Input, Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';
import _ from 'lodash';
import CommonTable from '../../components/CommonTable';
import { useDeleteInspectionRecordByProductIdMutation, useGetAllInspectionsQuery } from '../../services/inference';
import { backendAutoGenTimeToDisplayString, toLocalISOString } from '../../common/util';
import i18n from '../../i18n';
import { customZhCNDatePickerLocale, localStorageKeys, serverHost } from '../../common/const';
import enUS from 'antd/es/date-picker/locale/en_US';
import { aoiAlert, ALERT_TYPES } from '../../common/alert';
import { useScanBarcodeMutation } from '../../services/product';

const { RangePicker } = DatePicker;

const AllInspectionList = () => {
  const { t } = useTranslation();
  const [scanBarcode] = useScanBarcodeMutation();
  const [deleteInspectionRecord] = useDeleteInspectionRecordByProductIdMutation();

  const [searchSerialNumber, setSearchSerialNumber] = useState('');
  const [displaySearchSN, setDisplaySearchSN] = useState('');
  const [startTime, setStartTime] = useState(null);
  const [endTime, setEndTime] = useState(null);
  const [onlyFeedbackProvided, setOnlyFeedbackProvided] = useState(false);
  const [onlyDefectiveItems, setOnlyDefectiveItems] = useState(true);
  const [pagination, setPagination] = useState({ current: 1, pageSize: 0 });

  const [query, setQuery] = useState({});

  const { data: res, isFetching,
    refetch: refetchInspections,
  } = useGetAllInspectionsQuery(query);

  useEffect(() => {
    const q = {
      page: pagination.current - 1,
      limit: pagination.pageSize,
    };
    if (searchSerialNumber) q.serial_no = searchSerialNumber;
    if (startTime) q.start_datetime = toLocalISOString(new Date(startTime));
    if (endTime) q.end_datetime = toLocalISOString(new Date(endTime));
    if (onlyFeedbackProvided) q.feedback = true;
    if (onlyDefectiveItems) q.defect = true;
    q.t = Date.now().valueOf();

    setQuery(q);
  }, [pagination, searchSerialNumber, startTime, endTime, onlyFeedbackProvided, onlyDefectiveItems]);

  // const query = useMemo(() => {
  //   if (pagination.pageSize === 0) return undefined;
  //   const q = {
  //     page: pagination.current - 1,
  //     limit: pagination.pageSize,
  //   };
  //   if (searchSerialNumber) q.serial_no = searchSerialNumber;
  //   if (startTime) q.start_datetime = toLocalISOString(new Date(startTime));
  //   if (endTime) q.end_datetime = toLocalISOString(new Date(endTime));
  //   if (onlyFeedbackProvided) q.feedback = true;
  //   if (onlyDefectiveItems) q.defect = true;
  //   return q;
  // }, [pagination, searchSerialNumber, startTime, endTime, onlyFeedbackProvided, onlyDefectiveItems]);

  const data = useMemo(() => _.get(res, 'data', []), [res]);
  const total = useMemo(() => {
    const pageCount = Number(_.get(res, 'pageCount', 0));
    return pageCount * pagination.pageSize;
  }, [res, pagination.pageSize]);


  const cols = [
    {
      title: t('worklist.serialNo'),
      key: 'serialNo',
      render: (text, record) => (
        <span className='font-source text-[12px] font-normal'>
          {_.get(record, 'product_serial_no', '')}
        </span>
      ),
    },
    {
      title: t('worklist.date'),
      key: 'date',
      render: (text, record) => (
        <span className='font-source text-[12px] font-normal'>
          {_.get(record, 'timestamp') ? backendAutoGenTimeToDisplayString(record.timestamp) : ''}
        </span>
      ),
    },
    {
      title: t('worklist.totalComponent'),
      key: 'total_component',
      render: (text, record) => (
        <span className='font-source text-[12px] font-normal'>
          {_.get(record, 'total_component_count', 0)}
        </span>
      ),
    },
    {
      title: t('worklist.okngCounts'),
      key: 'okng_counts',
      render: (text, record) => (
        <div className='flex flex-1 items-center'>
          <div className='flex w-[56px] items-center gap-1'>
            <div className='flex flex-col items-center justify-center'>
              <img className='w-[12px] h-[12px]' src='/icn/checkCircle_green.svg' alt='checkCircle' />
            </div>
            <span className='font-source text-[12px] font-normal'>
              {_.get(record, 'total_component_count', 0) - _.get(record, 'defective_component_count', 0)}
            </span>
          </div>
          <div className='flex w-[56px] items-center gap-1'>
            <div className='flex flex-col items-center justify-center'>
              <img className='w-[12px] h-[12px]' src='/icn/warning_red.svg' alt='warning' />
            </div>
            <span className='font-source text-[12px] font-normal'>
              {_.get(record, 'defective_component_count', 0)}
            </span>
          </div>
        </div>
      ),
    },
    {
      title: t('worklist.passFail'),
      key: 'pass_fail',
      render: (text, record) => (
        <div className='flex gap-2 items-center'>
          {_.get(record, 'defective_component_count', 0) > 0 ? (
            <>
              <div className='flex flex-col items-center justify-center'>
                <img className='w-[12px] h-[12px]' src='/icn/warning_red.svg' alt='warning' />
              </div>
              <span className='font-source text-[12px] font-semibold text-[#EB5757]'>
                {t('worklist.fail')}
              </span>
            </>
          ) : (
            <>
              <div className='flex flex-col items-center justify-center'>
                <img className='w-[12px] h-[12px]' src='/icn/checkCircle_green.svg' alt='check' />
              </div>
              <span className='font-source text-[12px] font-semibold text-[#27AE60]'>
                {t('worklist.pass')}
              </span>
            </>
          )}
        </div>
      ),
    },
    {
      title: t('worklist.feedback'),
      key: 'feedback',
      render: (text, record) => {
        // console.log('record', record);

        return (
        <>
          {record.feedback_provided ? (
            <div className='flex items-center gap-2'>
              <img className='w-[16px] h-[16px]' src='/icn/check_green.svg' alt='check' />
              <span className='font-source text-[12px] font-normal'>
                {t('worklist.provided')}
              </span>
            </div>
          ) : (
            <div className='flex items-center gap-2'>
              <span className='font-source text-[12px] font-normal'>
                {t('worklist.notProvided')}
              </span>
            </div>
          )}
        </>
      )},
    },
    {
      title: t('worklist.actions'),
      key: 'actions',
      render: (text, record) => (
        <div className='flex items-center gap-2'>
          <span
            className={`font-source text-[12px] font-normal leading-[150%] text-AOI-blue cursor-pointer
              italic hover:text-AOI-blue-hover hover:bg-[#333] rounded-[4px] transition-all duration-300 ease-in-out
              px-2`}
            onClick={() => {
              let url = `/inspection/review`;

              url += `?ipc-product-id=${_.get(record, 'product_id')}`;
              url += `&ipc-session-id=${_.get(record, 'ipc_session_id')}`;
              url += `&golden-product-id=${_.get(record, 'golden_product_id')}`;
              url += `&is-from-live=false`;
              const q = { ...query };
              if (q) {
                delete q.page;
                delete q.limit;
              }
              if (!_.isEmpty(q)) {
                url += `&query=${encodeURIComponent(JSON.stringify(q))}`;
              }
              window.location.href = url;
            }}
          >
            {t('worklist.review')}
          </span>
          <span
            className={`font-source text-[12px] font-normal leading-[150%] text-AOI-blue cursor-pointer
              italic hover:text-AOI-blue-hover hover:bg-[#333] rounded-[4px] transition-all duration-300 ease-in-out
              px-2`}
            onClick={async() => {
              await handleRescan(_.get(record, 'product_id'));
            }}
          >
            {t('worklist.rescan')}
          </span>
          <span
            className={`font-source text-[12px] font-normal leading-[150%] text-AOI-blue cursor-pointer
              italic hover:text-AOI-blue-hover hover:bg-[#333] rounded-[4px] transition-all duration-300 ease-in-out
              px-2`}
            onClick={async() => {
              await handleExportMESResult(_.get(record, 'product_id'));
            }}
          >
            {t('common.exportMESResult')}
          </span>
          <span
            className={`font-source text-[12px] font-normal leading-[150%] text-AOI-blue cursor-pointer
              italic hover:text-AOI-blue-hover hover:bg-[#333] rounded-[4px] transition-all duration-300 ease-in-out
              px-2`}
            onClick={async () => {
              await handleDeleteInspectionRecord(_.get(record, 'product_id'));
            }}
          >
            {t('common.delete')}
          </span>
        </div>
      ),
    },
  ];


  const handleRescan = async (productId) => {
    // Ben said this is unknown for now, so we will not implement this for now
    // const res = await scanBarcode({ device_id:  });
    const res = await scanBarcode();

    if (res.error) {
      aoiAlert(t('notification.error.scanBarcode'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    if (res) {
      if(!res.data) {
        aoiAlert(t('notification.error.scanBarcode'), ALERT_TYPES.COMMON_ERROR);
        return;
      }

      const updateRes = await updateProduct({
        product_id: Number(productId),
        product_serial: res?.data
      });


      if (updateRes.error) {
        aoiAlert(t('notification.error.updateProduct'), ALERT_TYPES.COMMON_ERROR);
        console.error('updateProduct error: ', updateRes.error.message);
        return;
      }

      const updateResData = _.get(updateRes, 'data.data', {});
      if (updateResData) {
        aoiAlert(t('notification.success.updateProduct'), ALERT_TYPES.COMMON_SUCCESS);
        setRefreshToggle(refreshToggle + 1);
      } else {
        aoiAlert(t('notification.error.updateProduct'), ALERT_TYPES.COMMON_ERROR);
      }
    }
  };

  const handleExportMESResult = async (productId) => {
    const res = await fetch(`${serverHost}/inspection/exportMesResult`, {
      method: 'POST',
      headers: {
        Authorization: localStorage.getItem(localStorageKeys.accessToken),
      },
      body: JSON.stringify(Number(productId)),
    });

    if (!res.ok) {
      aoiAlert(t('notification.error.exportMesResult'), ALERT_TYPES.COMMON_ERROR);
      console.error('exportMesResult error: ', res.statusText);
      return;
    }

    aoiAlert(t('notification.success.exportMesResult'), ALERT_TYPES.COMMON_INFO);
    return;
  };

  const handleDeleteInspectionRecord = async (productId) => {
    const res = await deleteInspectionRecord(productId);
    if (res.error) {
      aoiAlert(t('notification.error.deleteInspectionRecord'), ALERT_TYPES.COMMON_ERROR);
      console.error('deleteInspectionRecord error: ', res.error.message);
      return;
    }
    refetchInspections();
  }

  return (
    <div className='flex flex-col gap-4 flex-1 self-stretch items-start'>
      <div className='flex py-2 gap-4 self-stretch items-center'>
        <Input
          value={displaySearchSN}
          onChange={(e) => setDisplaySearchSN(e.target.value)}
          style={{ width: '200px' }}
          onBlur={(e) => setSearchSerialNumber(e.target.value)}
          onPressEnter={(e) => setSearchSerialNumber(e.target.value)}
          placeholder={t('worklist.searchBySerialNo')}
        />
        <RangePicker
          locale={i18n.language === 'cn' ? customZhCNDatePickerLocale : enUS}
          showTime
          onCalendarChange={(value) => {
            setStartTime(_.get(value, '0', null));
            setEndTime(_.get(value, '1', null));
          }}
          value={[startTime, endTime]}
        />
        <Button type={onlyFeedbackProvided ? 'primary' : 'default'} onClick={() => setOnlyFeedbackProvided(!onlyFeedbackProvided)}>
          <span className={`font-source text-[12px] font-normal ${onlyFeedbackProvided ? 'text-[#333]' : 'text-white'}`}>
            {t('worklist.feedbackProvided')}
          </span>
        </Button>
        <Button type={onlyDefectiveItems ? 'primary' : 'default'} onClick={() => setOnlyDefectiveItems(!onlyDefectiveItems)}>
          <span className={`font-source text-[12px] font-normal ${onlyDefectiveItems ? 'text-[#333]' : 'text-white'}`}>
            {t('worklist.onlyDisplayDefectiveItems')}
          </span>
        </Button>
        <Tooltip
          title={<span className='font-source text-[12px] font-normal'>{t('worklist.clearFilter')}</span>}
        >
          <div
            className='flex w-[32px] h-[32px] justify-center items-center cursor-pointer'
            onClick={() => {
              setStartTime(null);
              setEndTime(null);
              setOnlyFeedbackProvided(false);
              setOnlyDefectiveItems(false);
            }}
          >
            <img className='w-[16px] h-[16px]' src='/icn/cancelFilter_blue.svg' alt='cancelFilter' />
          </div>
        </Tooltip>
      </div>
      <CommonTable
        cols={cols}
        data={data}
        isLoading={isFetching}
        setPageSize={(pageSize) => setPagination((prev) => ({ ...prev, pageSize }))}
        total={total}
        onPageChange={(page) => setPagination((prev) => ({ ...prev, current: page }))}
      />
    </div>
  );
};

export default AllInspectionList;